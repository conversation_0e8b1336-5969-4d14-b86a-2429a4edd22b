part of xr_helper;

class NetworkApiServices extends BaseApiServices {
  static const _timeOutDuration = Duration(seconds: 30);

  // * Get request ================================
  @override
  Future<dynamic> getResponse(String url) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.i('GetApiUrl => $apiUrl');

      final response =
          await http.get(apiUrl, headers: headers).timeout(_timeOutDuration);

      Log.f(
          'GetAPIUrl => $apiUrl\nGetRes => ${response.body}\nStatus Code => ${response.statusCode}');

      responseJson = returnResponse(response);
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
    return responseJson;
  }

  // * Post request ================================
  @override
  Future postResponse(
    String url, {
    required Map<String, dynamic> body,
    List<PlatformFile> filePaths = const [],
    String? fieldName,
  }) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.w(
          'PostApiUrl => $apiUrl\n 💾💾💾 PostData -> $body 💾💾💾 & filePaths => $filePaths');

      late http.Response response;

      response = await _postOrPutAndUploadFiles(
        apiUrl,
        body: body,
        filePaths: filePaths,
        fieldName: fieldName,
        method: 'POST',
      );

      Log.f(
          'PostRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = await jsonDecode(response.body);

        return responseJson;
      } else {
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
  }

  // * Put request ================================
  @override
  Future putResponse(
    String url, {
    required Map<String, dynamic> data,
    List<PlatformFile> filePaths = const [],
    String? fieldName,
  }) async {
    dynamic responseJson;
    try {
      final apiUrl = Uri.parse(url);

      Log.w(
          'PutApiUrl => $apiUrl\n 💾💾💾 PutData -> $data 💾💾💾 & filePaths => $filePaths');

      late http.Response response;

      if (filePaths.isNotEmpty) {
        response = await _postOrPutAndUploadFiles(
          apiUrl,
          body: data,
          filePaths: filePaths,
          fieldName: fieldName,
          method: 'PUT',
        );
      } else {
        response = await http
            .put(apiUrl, body: jsonEncode(data), headers: headers)
            .timeout(_timeOutDuration);
      }

      Log.f(
          'PutRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        responseJson = await jsonDecode(response.body);
      } else {
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
    return responseJson;
  }

  // * Post Or Put & Upload File request ================================
  Future<http.Response> _postOrPutAndUploadFiles(
    Uri url, {
    required Map<String, dynamic> body,
    required List<PlatformFile>
        filePaths, // accept List<String> (paths) or PlatformFile/XFile/Map with bytes
    required String method,
    String? fieldName,
  }) async {
    final request = http.MultipartRequest(method, url);

    // keep existing headers
    request.headers.addAll(headers);

    final name = fieldName ?? 'attachment[]';

    for (final file in filePaths) {
      // 2) file_picker PlatformFile (web: .bytes, mobile: .path)
      if (kIsWeb || file.bytes != null) {
        final bytes = file.bytes!;
        request.files.add(http.MultipartFile.fromBytes(
          name,
          bytes,
          filename: file.name,
        ));
      } else if (file.path != null) {
        request.files.add(await http.MultipartFile.fromPath(name, file.path!,
            filename: file.name));
      }
      continue;
    }

    // add fields
    final Map<String, String> fieldsData =
        body.map((k, v) => MapEntry(k, v.toString()));
    request.fields.addAll(fieldsData);

    final streamed = await request.send();
    final response =
        await http.Response.fromStream(streamed).timeout(_timeOutDuration);

    Log.f('PostRes => ${response.body} => ${response.statusCode}');
    return response;
  }

  // Future _postOrPutAndUploadFiles(
  //   Uri url, {
  //   required Map<String, dynamic> body,
  //   required List<PlatformFile> filePaths,
  //   required String method,
  //   String? fieldName,
  // }) async {
  //   late http.Response response;
  //
  //   http.MultipartRequest request = http.MultipartRequest(method, url);
  //
  //   //? Add headers
  //   request.headers.addAll(headers);
  //
  //   //? Add files
  //   for (var image in filePaths) {
  //     request.files
  //         .add(await http.MultipartFile.fromPath(fieldName ?? 'image', image));
  //   }
  //
  //   final Map<String, String> fieldsData = body.map((key, value) {
  //     return MapEntry(key, value.toString());
  //   });
  //
  //   request.fields.addAll(fieldsData);
  //
  //   response = await http.Response.fromStream(await request.send())
  //       .timeout(_timeOutDuration);
  //
  //   Log.f(
  //       'PostRes => ${response.body}\nStatus                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 => ${response.statusCode}');
  //
  //   return response;
  // }

  // * Delete request ================================
  @override
  Future deleteResponse(String url) async {
    try {
      final apiUrl = Uri.parse(url);

      Log.i('DeleteApiUrl => $apiUrl');

      final response =
          await http.delete(apiUrl, headers: headers).timeout(_timeOutDuration);

      Log.f(
          'DeleteRes => ${response.body}\nStatus Code => ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw FetchDataException(response.body);
      }
    } on SocketException {
      throw const SocketException(ApiMessages.noInternetConnection);
    } catch (e) {
      throw FetchDataException(e.toString());
    }
  }
}
