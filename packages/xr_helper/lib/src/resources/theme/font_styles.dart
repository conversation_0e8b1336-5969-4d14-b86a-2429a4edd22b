part of xr_helper;

TextTheme textTheme(context) => Theme.of(context).textTheme;

//! Headline & SubHeadLine Styles
TextStyle headLineStyle(context, {bool isWhiteText = false}) => textTheme(
      context,
    ).titleLarge!.copyWith(
          color: isWhiteText ? Colors.white : AppColors.black,
          fontWeight: FontWeight.bold,
          fontSize: 28,
        );

TextStyle subHeadLineStyle(context, {bool isWhiteText = false}) => textTheme(
      context,
    ).titleLarge!.copyWith(
          fontSize: 24,
          color: isWhiteText ? Colors.white : AppColors.black,
          fontWeight: FontWeight.bold,
        );

//!----------------------------------------------------------------------------

//! Title & Subtitle Styles
TextStyle titleStyle(context, {bool isWhiteText = false}) => textTheme(
      context,
    ).titleMedium!.copyWith(
        color: isWhiteText ? Colors.white : AppColors.black, fontSize: 18);

TextStyle subTitleStyle(context, {bool isWhiteText = false}) => textTheme(
      context,
    ).titleSmall!.copyWith(
        color: isWhiteText ? Colors.white : AppColors.black, fontSize: 16);

//! Label Styles
TextStyle labelLargeStyle(context, {bool isWhiteText = false}) => textTheme(
      context,
    ).labelLarge!.copyWith(
          color: isWhiteText ? Colors.white : AppColors.black,
          fontSize: 14,
        );

TextStyle labelMediumStyle(context, {bool isWhiteText = false}) => textTheme(
      context,
    ).labelMedium!.copyWith(
          color: isWhiteText ? Colors.white : AppColors.black,
          fontSize: 14,
        );

TextStyle labelSmallStyle(context, {bool isWhiteText = false}) => textTheme(
      context,
    ).labelSmall!.copyWith(
          color: isWhiteText ? Colors.white : AppColors.black,
        );

//! Body Styles
TextStyle bodyStyle(context, {bool isWhiteText = false}) =>
    textTheme(context).bodyLarge!.copyWith(
          color: isWhiteText ? Colors.white : AppColors.black,
        );

TextStyle hintStyle(context, {bool isWhiteText = false}) =>
    textTheme(context).bodySmall!.copyWith(
          color: isWhiteText ? Colors.white : AppColors.black,
          fontSize: 16,
        );

// class AppTextStyles {
//   static const String _fontFamily = 'YourFontFamily';
//
//   static const TextStyle headLineStyle = TextStyle(
//     fontWeight: FontWeight.bold,
//     fontSize: 28,
//     color: Colors.black,
//     fontFamily: _fontFamily,
//   );
//
//   static const TextStyle subHeadLineStyle = TextStyle(
//     fontFamily: _fontFamily,
//     fontSize: 24,
//     color: Colors.black,
//     fontWeight: FontWeight.bold,
//   );
//
//   static const TextStyle titleStyle = TextStyle(
//     fontFamily: _fontFamily,
//     color: Colors.black,
//     fontSize: 18,
//   );
//
//   static const TextStyle subTitleStyle = TextStyle(
//     fontFamily: _fontFamily,
//     color: Colors.black,
//     fontSize: 16,
//   );
//
//   static const TextStyle labelLargeStyle = TextStyle(
//     fontFamily: _fontFamily,
//     color: Colors.black,
//     fontSize: 14,
//   );
//
//   static const TextStyle labelMediumStyle = TextStyle(
//     fontFamily: _fontFamily,
//     color: Colors.black,
//     fontSize: 14,
//   );
//
//   static const TextStyle labelSmallStyle = TextStyle(
//     fontFamily: _fontFamily,
//     color: Colors.black,
//   );
//
//   static const TextStyle bodyStyle = TextStyle(
//     fontFamily: _fontFamily,
//     color: Colors.black,
//   );
//
//   static const TextStyle hintStyle = TextStyle(
//     fontFamily: _fontFamily,
//     color: Colors.black,
//     fontSize: 16,
//   );
// }
