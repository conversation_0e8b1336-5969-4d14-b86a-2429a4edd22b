part of xr_helper;

class Validations {
  //! Password Validation
  static String? password(
    String? value, {
    String? emptyPasswordMessage = "هذا الحقل مطلوب",
    String? passwordLengthMessage = "يجب أن تكون كلمة المرور 8 أحرف على الأقل",
  }) {
    if (value == null || value.isEmpty) {
      return emptyPasswordMessage;
    } else if (value.length < 8) {
      return passwordLengthMessage;
    }
    return null;
  }

  //! Phone Number Validation
  static String? phoneNumber(value,
      {String? emptyPhoneMessage = "هذا الحقل مطلوب",
      String? phoneLengthMessage = "برجاء إدخال رقم هاتف صحيح"}) {
    String pattern = r'(^(?:[0]9)?[0-9]{1,12}$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyPhoneMessage;
    } else if (!regExp.hasMatch(value)) {
      return phoneLengthMessage;
    }
    return null;
  }

  //! Numbers Only Validation
  static String? numbersOnly(value,
      {String? emptyMessage = "هذا الحقل مطلوب",
      String? invalidMessage = "يجب أن يحتوي هذا الحقل على أرقام فقط"}) {
    String pattern = r'(^[0-9]*$)';
    RegExp regExp = RegExp(pattern);
    if (value == null || value.isEmpty) {
      return emptyMessage;
    } else if (!regExp.hasMatch(value)) {
      return invalidMessage;
    }
    return null;
  }

  //! Email Validation
  static String? email(
    String? value, {
    String? emptyEmailMessage = "هذا الحقل مطلوب",
    String? invalidEmailMessage = "برجاء إدخال بريد إلكتروني صحيح",
  }) {
    final RegExp urlExp = RegExp(
        r"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    if (value == null || value.isEmpty) {
      return emptyEmailMessage;
    } else if (!urlExp.hasMatch(value)) {
      return invalidEmailMessage;
    }
    return null;
  }

  //! Must Be Not Empty Validation
  static String? mustBeNotEmpty(String? value, {String? emptyMessage}) {
    if (value == null || value.isEmpty) {
      return emptyMessage ?? "هذا الحقل مطلوب";
    }
    return null;
  }
}
