part of xr_helper;

class BaseList<T> extends StatelessWidget {
  final List<T> data;
  final Widget Function(T data, int index) itemBuilder;
  final Widget separatorGap;
  final bool isHorizontal;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final bool showEmptyWidget;
  final Alignment? alignment;
  final ScrollPhysics? physics;
  final Widget? emptyWidget;
  final Function()? onLoadMore;
  final bool isLoading;
  final Widget? loadingWidget;
  final ScrollController? controller;

  const BaseList({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = AppGaps.mediumGap,
    this.isHorizontal = false,
    this.showEmptyWidget = true,
    this.height,
    this.emptyWidget,
    this.padding,
    this.physics,
    this.alignment,
    this.onLoadMore,
    this.loadingWidget,
    this.controller,
    this.isLoading = false,
  });

  const BaseList.horizontal({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = AppGaps.mediumGap,
    this.isHorizontal = true,
    this.physics,
    this.padding,
    this.emptyWidget,
      this.controller,
    this.showEmptyWidget = true,
    this.isLoading = false,
    required this.height,
    this.alignment,
    this.loadingWidget,
    this.onLoadMore,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading && data.isEmpty && loadingWidget != null) {
      return loadingWidget!;
    }

    if (data.isEmpty && showEmptyWidget && emptyWidget != null) {
      return emptyWidget!;
    }

    if (data.isEmpty) return const SizedBox.shrink();

    Widget baseList({
      bool isHorizontal = false,
    }) {
      // if (onLoadMore != null) {
      //   return PaginatedList<T>(
      //     loadingIndicator: const LoadingWidget(),
      //     items: data,
      //     isRecentSearch: false,
      //     shrinkWrap: true,
      //     isLastPage: CursorProvider().lastPaginate,
      //     onLoadMore: (index) => onLoadMore!(),
      //     builder: itemBuilder,
      //   );
      // }

      return ListView.separated(
        padding: padding,
        shrinkWrap: true,
        controller: controller,
        physics: physics,
        itemCount: data.length,
        scrollDirection: isHorizontal ? Axis.horizontal : Axis.vertical,
        itemBuilder: (context, index) {
          return itemBuilder(data[index], index);
        },
        separatorBuilder: (context, index) => separatorGap,
      );
    }

    if (isHorizontal) {
      return baseList(isHorizontal: true).decorated(
        height: height,
        // alignment: alignment ??
        //     (AppConsts.isEnglish(ref)
        //         ? Alignment.centerLeft
        //         : Alignment.centerRight),
      );
    }

    return baseList();
  }
}
