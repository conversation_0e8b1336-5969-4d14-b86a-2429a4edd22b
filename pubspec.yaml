name: dawraq
description: "A new Flutter project."
publish_to: 'none'

version: 1.0.8+8
#version: 1.0.6+6

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # * Best Package *
  xr_helper:
    path: packages/xr_helper


  #? Localization
  flutter_localizations:
    sdk: flutter

  #? UI
  cupertino_icons: ^1.0.8
  flutter_screenutil: ^5.9.3
  intl: ^0.20.2
  google_fonts: ^6.2.1
  dropdown_search: ^5.0.6

  #? State Management
  flutter_riverpod: ^2.5.1
  riverpod: ^2.5.1
  hooks_riverpod: ^2.5.1
  flutter_hooks: ^0.20.5
  collection: ^1.18.0
  equatable: ^2.0.5

  #? No Screenshots & Recordings
  no_screenshot: ^0.2.0

  #? Media Picker
  file_picker: ^10.3.3
  open_filex: ^4.7.0
  image_picker: ^1.1.2
  permission_handler: ^11.3.1
  url_launcher: ^6.3.1

  #? Video Player
  better_player_plus: ^1.0.8
#  better_player: ^0.0.84
  chewie: ^1.8.5
#  chewie:
#    path: packages/chewie

  #? HTML
  flutter_widget_from_html_core: ^0.15.2
  universal_html: ^2.2.4

  #? Loading
  shimmer: ^3.0.0
  loading_animation_widget: ^1.2.1

  #? Utils

dependency_overrides:
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^4.0.0
  build_runner:
  flutter_gen_runner:
  flutter_gen: ^5.6.0

  flutter_launcher_icons: ^0.13.1
  flutter_native_splash: ^2.4.1
#? dart run build_runner build --delete-conflicting-outputs

#? dart run flutter_native_splash:create
flutter_native_splash:
  image: assets/images/app_logo.png
  color: "#FFFFFF"

#? dart run flutter_launcher_icons:main
flutter_launcher_icons:
  android: false
  ios: true
  remove_alpha_ios: true
  color: "#000000"
  image_path: "assets/images/logo.jpg"

flutter_gen:
  output: lib/generated/ # Optional (default: lib/gen/)

  # Optional
  integrations:
    flutter_svg: true
    lottie: true

flutter:
  uses-material-design: true

  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animated/
    - shorebird.yaml

flutter_intl:
  enabled: true
