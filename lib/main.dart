import 'package:dawraq/src/base_app.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import "package:universal_html/html.dart" as html;

void disableRightClick() {
  html.document.onContextMenu.listen((event) => event.preventDefault());
}

void blockDevTools() {
  html.window.console.log('DevTools is not allowed!');
  html.window.onKeyDown.listen((event) {
    if (event.ctrlKey && (event.keyCode == 73 || event.keyCode == 74)) {
      event.preventDefault();
    }
  });
}

//! flutter build web --web-renderer html

void main() {
  if (kIsWeb) {
    disableRightClick();
    blockDevTools();
  }
  runApp(const ProviderScope(
    child: BaseApp(),
  ));
}
