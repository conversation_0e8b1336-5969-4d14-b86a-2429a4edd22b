// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a en locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'en';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "aboutUs": MessageLookupByLibrary.simpleMessage("استفسارات علمية ومهنية"),
    "addAnswer": MessageLookupByLibrary.simpleMessage("إضافة إجابة"),
    "aiAnalysis": MessageLookupByLibrary.simpleMessage(
      "التحليل بالذكاء الاصطناعي",
    ),
    "aiCanGenerateAnswer": MessageLookupByLibrary.simpleMessage(
      "يمكن للذكاء الاصطناعي توليد إجابة تلقائياً",
    ),
    "aiGenerated": MessageLookupByLibrary.simpleMessage(
      "مولد بالذكاء الاصطناعي",
    ),
    "answer": MessageLookupByLibrary.simpleMessage("الجواب"),
    "answerCanBeEmpty": MessageLookupByLibrary.simpleMessage(
      "يمكنك ترك الإجابة فارغة وإضافتها لاحقاً",
    ),
    "answerGeneratedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم توليد الإجابة بنجاح",
    ),
    "answerOptional": MessageLookupByLibrary.simpleMessage(
      "الإجابة (اختيارية)",
    ),
    "answerSavedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم حفظ الإجابة بنجاح",
    ),
    "areYouSureToSignOut": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من تسجيل الخروج؟",
    ),
    "areYouSureYouWantToExitApp": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من الخروج من التطبيق؟",
    ),
    "areYouSureYouWantToLogout": MessageLookupByLibrary.simpleMessage(
      "هل أنت متأكد من تسجيل الخروج؟",
    ),
    "camera": MessageLookupByLibrary.simpleMessage("الكاميرا"),
    "cancel": MessageLookupByLibrary.simpleMessage("إلغاء"),
    "clickPlusToAdd": MessageLookupByLibrary.simpleMessage(
      "اضغط على زر + لإضافة سؤال جديد",
    ),
    "confirm": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "contactUs": MessageLookupByLibrary.simpleMessage("تواصل معنا"),
    "content": MessageLookupByLibrary.simpleMessage("التجارب"),
    "contentInfo": MessageLookupByLibrary.simpleMessage("معلومات المحتوى"),
    "country": MessageLookupByLibrary.simpleMessage("البلد"),
    "createNewQuestion": MessageLookupByLibrary.simpleMessage(
      "إنشاء سؤال جديد",
    ),
    "createQuestion": MessageLookupByLibrary.simpleMessage("إنشاء السؤال"),
    "creating": MessageLookupByLibrary.simpleMessage("جاري الإنشاء..."),
    "difficulty": MessageLookupByLibrary.simpleMessage("الصعوبة"),
    "editAnswer": MessageLookupByLibrary.simpleMessage("تعديل الإجابة"),
    "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
    "enter": MessageLookupByLibrary.simpleMessage("ادخل"),
    "errorCreatingQuestion": MessageLookupByLibrary.simpleMessage(
      "خطأ في إنشاء السؤال",
    ),
    "errorGeneratingAnswer": MessageLookupByLibrary.simpleMessage(
      "خطأ في توليد الإجابة",
    ),
    "errorSavingAnswer": MessageLookupByLibrary.simpleMessage(
      "خطأ في حفظ الإجابة",
    ),
    "exitApp": MessageLookupByLibrary.simpleMessage("الخروج من التطبيق"),
    "explanation": MessageLookupByLibrary.simpleMessage("الشرح"),
    "gallery": MessageLookupByLibrary.simpleMessage("المعرض"),
    "generateAiAnswer": MessageLookupByLibrary.simpleMessage(
      "توليد إجابة بالذكاء الاصطناعي",
    ),
    "generating": MessageLookupByLibrary.simpleMessage("جاري التوليد..."),
    "job": MessageLookupByLibrary.simpleMessage("الوظيفة"),
    "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "manual": MessageLookupByLibrary.simpleMessage("يدوي"),
    "messageSentSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إرسال الرسالة بنجاح",
    ),
    "name": MessageLookupByLibrary.simpleMessage("الاسم"),
    "no": MessageLookupByLibrary.simpleMessage("لا"),
    "noContent": MessageLookupByLibrary.simpleMessage("لا توجد تجارب"),
    "noQuestionsAvailable": MessageLookupByLibrary.simpleMessage(
      "لا توجد أسئلة متاحة",
    ),
    "note": MessageLookupByLibrary.simpleMessage("الملاحظة"),
    "ok": MessageLookupByLibrary.simpleMessage("موافق"),
    "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
    "perPage": MessageLookupByLibrary.simpleMessage("عدد العناصر في الصفحة"),
    "phone": MessageLookupByLibrary.simpleMessage("الهاتف"),
    "phoneIsInvalid": MessageLookupByLibrary.simpleMessage("الهاتف غير صحيح"),
    "pleaseEnterQuestion": MessageLookupByLibrary.simpleMessage(
      "يرجى إدخال السؤال",
    ),
    "question": MessageLookupByLibrary.simpleMessage("السؤال"),
    "questionCreatedSuccessfully": MessageLookupByLibrary.simpleMessage(
      "تم إنشاء السؤال بنجاح",
    ),
    "questionRequired": MessageLookupByLibrary.simpleMessage("السؤال *"),
    "questionTooShort": MessageLookupByLibrary.simpleMessage(
      "يجب أن يكون السؤال أكثر من 10 أحرف",
    ),
    "questionsAndAnswers": MessageLookupByLibrary.simpleMessage(
      "الأسئلة والأجوبة",
    ),
    "required": MessageLookupByLibrary.simpleMessage("هذا الحقل مطلوب"),
    "save": MessageLookupByLibrary.simpleMessage("حفظ"),
    "saving": MessageLookupByLibrary.simpleMessage("جاري الحفظ..."),
    "schoolID": MessageLookupByLibrary.simpleMessage("رقم المدرسة"),
    "schoolIsNotActive": MessageLookupByLibrary.simpleMessage(
      "المدرسة غير نشطة",
    ),
    "schoolName": MessageLookupByLibrary.simpleMessage("اسم المدرسة"),
    "schoolNumber": MessageLookupByLibrary.simpleMessage("رقم المدرسة"),
    "search": MessageLookupByLibrary.simpleMessage("بحث"),
    "signIn": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
    "signOut": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
    "submit": MessageLookupByLibrary.simpleMessage("تأكيد"),
    "tips": MessageLookupByLibrary.simpleMessage("نصائح:"),
    "type": MessageLookupByLibrary.simpleMessage("النوع"),
    "writeAnswerHere": MessageLookupByLibrary.simpleMessage(
      "اكتب الإجابة هنا (اختيارية)...",
    ),
    "writeClearQuestion": MessageLookupByLibrary.simpleMessage(
      "اكتب سؤالاً واضحاً ومفهوماً",
    ),
    "writeQuestionHere": MessageLookupByLibrary.simpleMessage(
      "اكتب السؤال هنا...",
    ),
    "yes": MessageLookupByLibrary.simpleMessage("نعم"),
  };
}
