// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `تسجيل الدخول`
  String get signIn {
    return Intl.message('تسجيل الدخول', name: 'signIn', desc: '', args: []);
  }

  /// `تسجيل الخروج`
  String get signOut {
    return Intl.message('تسجيل الخروج', name: 'signOut', desc: '', args: []);
  }

  /// `هل أنت متأكد من تسجيل الخروج؟`
  String get areYouSureToSignOut {
    return Intl.message(
      'هل أنت متأكد من تسجيل الخروج؟',
      name: 'areYouSureToSignOut',
      desc: '',
      args: [],
    );
  }

  /// `نعم`
  String get yes {
    return Intl.message('نعم', name: 'yes', desc: '', args: []);
  }

  /// `لا`
  String get no {
    return Intl.message('لا', name: 'no', desc: '', args: []);
  }

  /// `إلغاء`
  String get cancel {
    return Intl.message('إلغاء', name: 'cancel', desc: '', args: []);
  }

  /// `موافق`
  String get ok {
    return Intl.message('موافق', name: 'ok', desc: '', args: []);
  }

  /// `التجارب`
  String get content {
    return Intl.message('التجارب', name: 'content', desc: '', args: []);
  }

  /// `لا توجد تجارب`
  String get noContent {
    return Intl.message('لا توجد تجارب', name: 'noContent', desc: '', args: []);
  }

  /// `المدرسة غير نشطة`
  String get schoolIsNotActive {
    return Intl.message(
      'المدرسة غير نشطة',
      name: 'schoolIsNotActive',
      desc: '',
      args: [],
    );
  }

  /// `الشرح`
  String get explanation {
    return Intl.message('الشرح', name: 'explanation', desc: '', args: []);
  }

  /// `تأكيد`
  String get confirm {
    return Intl.message('تأكيد', name: 'confirm', desc: '', args: []);
  }

  /// `البريد الإلكتروني`
  String get email {
    return Intl.message('البريد الإلكتروني', name: 'email', desc: '', args: []);
  }

  /// `كلمة المرور`
  String get password {
    return Intl.message('كلمة المرور', name: 'password', desc: '', args: []);
  }

  /// `تأكيد`
  String get submit {
    return Intl.message('تأكيد', name: 'submit', desc: '', args: []);
  }

  /// `بحث`
  String get search {
    return Intl.message('بحث', name: 'search', desc: '', args: []);
  }

  /// `تسجيل الخروج`
  String get logout {
    return Intl.message('تسجيل الخروج', name: 'logout', desc: '', args: []);
  }

  /// `هل أنت متأكد من تسجيل الخروج؟`
  String get areYouSureYouWantToLogout {
    return Intl.message(
      'هل أنت متأكد من تسجيل الخروج؟',
      name: 'areYouSureYouWantToLogout',
      desc: '',
      args: [],
    );
  }

  /// `الخروج من التطبيق`
  String get exitApp {
    return Intl.message(
      'الخروج من التطبيق',
      name: 'exitApp',
      desc: '',
      args: [],
    );
  }

  /// `هل أنت متأكد من الخروج من التطبيق؟`
  String get areYouSureYouWantToExitApp {
    return Intl.message(
      'هل أنت متأكد من الخروج من التطبيق؟',
      name: 'areYouSureYouWantToExitApp',
      desc: '',
      args: [],
    );
  }

  /// `عدد العناصر في الصفحة`
  String get perPage {
    return Intl.message(
      'عدد العناصر في الصفحة',
      name: 'perPage',
      desc: '',
      args: [],
    );
  }

  /// `تواصل معنا`
  String get contactUs {
    return Intl.message('تواصل معنا', name: 'contactUs', desc: '', args: []);
  }

  /// `الكاميرا`
  String get camera {
    return Intl.message('الكاميرا', name: 'camera', desc: '', args: []);
  }

  /// `المعرض`
  String get gallery {
    return Intl.message('المعرض', name: 'gallery', desc: '', args: []);
  }

  /// `الملاحظة`
  String get note {
    return Intl.message('الملاحظة', name: 'note', desc: '', args: []);
  }

  /// `الاسم`
  String get name {
    return Intl.message('الاسم', name: 'name', desc: '', args: []);
  }

  /// `الهاتف`
  String get phone {
    return Intl.message('الهاتف', name: 'phone', desc: '', args: []);
  }

  /// `ادخل`
  String get enter {
    return Intl.message('ادخل', name: 'enter', desc: '', args: []);
  }

  /// `استفسارات علمية ومهنية`
  String get aboutUs {
    return Intl.message(
      'استفسارات علمية ومهنية',
      name: 'aboutUs',
      desc: '',
      args: [],
    );
  }

  /// `رقم المدرسة`
  String get schoolNumber {
    return Intl.message(
      'رقم المدرسة',
      name: 'schoolNumber',
      desc: '',
      args: [],
    );
  }

  /// `اسم المدرسة`
  String get schoolName {
    return Intl.message('اسم المدرسة', name: 'schoolName', desc: '', args: []);
  }

  /// `الوظيفة`
  String get job {
    return Intl.message('الوظيفة', name: 'job', desc: '', args: []);
  }

  /// `البلد`
  String get country {
    return Intl.message('البلد', name: 'country', desc: '', args: []);
  }

  /// `رقم المدرسة`
  String get schoolID {
    return Intl.message('رقم المدرسة', name: 'schoolID', desc: '', args: []);
  }

  /// `هذا الحقل مطلوب`
  String get required {
    return Intl.message(
      'هذا الحقل مطلوب',
      name: 'required',
      desc: '',
      args: [],
    );
  }

  /// `الهاتف غير صحيح`
  String get phoneIsInvalid {
    return Intl.message(
      'الهاتف غير صحيح',
      name: 'phoneIsInvalid',
      desc: '',
      args: [],
    );
  }

  /// `تم إرسال الرسالة بنجاح`
  String get messageSentSuccessfully {
    return Intl.message(
      'تم إرسال الرسالة بنجاح',
      name: 'messageSentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `التحليل بالذكاء الاصطناعي`
  String get aiAnalysis {
    return Intl.message(
      'التحليل بالذكاء الاصطناعي',
      name: 'aiAnalysis',
      desc: '',
      args: [],
    );
  }

  /// `الأسئلة والأجوبة`
  String get questionsAndAnswers {
    return Intl.message(
      'الأسئلة والأجوبة',
      name: 'questionsAndAnswers',
      desc: '',
      args: [],
    );
  }

  /// `السؤال`
  String get question {
    return Intl.message('السؤال', name: 'question', desc: '', args: []);
  }

  /// `الجواب`
  String get answer {
    return Intl.message('الجواب', name: 'answer', desc: '', args: []);
  }

  /// `الصعوبة`
  String get difficulty {
    return Intl.message('الصعوبة', name: 'difficulty', desc: '', args: []);
  }

  /// `النوع`
  String get type {
    return Intl.message('النوع', name: 'type', desc: '', args: []);
  }

  /// `مولد بالذكاء الاصطناعي`
  String get aiGenerated {
    return Intl.message(
      'مولد بالذكاء الاصطناعي',
      name: 'aiGenerated',
      desc: '',
      args: [],
    );
  }

  /// `يدوي`
  String get manual {
    return Intl.message('يدوي', name: 'manual', desc: '', args: []);
  }

  /// `توليد إجابة بالذكاء الاصطناعي`
  String get generateAiAnswer {
    return Intl.message(
      'توليد إجابة بالذكاء الاصطناعي',
      name: 'generateAiAnswer',
      desc: '',
      args: [],
    );
  }

  /// `تعديل الإجابة`
  String get editAnswer {
    return Intl.message(
      'تعديل الإجابة',
      name: 'editAnswer',
      desc: '',
      args: [],
    );
  }

  /// `إضافة إجابة`
  String get addAnswer {
    return Intl.message('إضافة إجابة', name: 'addAnswer', desc: '', args: []);
  }

  /// `حفظ`
  String get save {
    return Intl.message('حفظ', name: 'save', desc: '', args: []);
  }

  /// `جاري التوليد...`
  String get generating {
    return Intl.message(
      'جاري التوليد...',
      name: 'generating',
      desc: '',
      args: [],
    );
  }

  /// `جاري الحفظ...`
  String get saving {
    return Intl.message('جاري الحفظ...', name: 'saving', desc: '', args: []);
  }

  /// `تم توليد الإجابة بنجاح`
  String get answerGeneratedSuccessfully {
    return Intl.message(
      'تم توليد الإجابة بنجاح',
      name: 'answerGeneratedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `تم حفظ الإجابة بنجاح`
  String get answerSavedSuccessfully {
    return Intl.message(
      'تم حفظ الإجابة بنجاح',
      name: 'answerSavedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `خطأ في توليد الإجابة`
  String get errorGeneratingAnswer {
    return Intl.message(
      'خطأ في توليد الإجابة',
      name: 'errorGeneratingAnswer',
      desc: '',
      args: [],
    );
  }

  /// `خطأ في حفظ الإجابة`
  String get errorSavingAnswer {
    return Intl.message(
      'خطأ في حفظ الإجابة',
      name: 'errorSavingAnswer',
      desc: '',
      args: [],
    );
  }

  /// `إنشاء سؤال جديد`
  String get createNewQuestion {
    return Intl.message(
      'إنشاء سؤال جديد',
      name: 'createNewQuestion',
      desc: '',
      args: [],
    );
  }

  /// `معلومات المحتوى`
  String get contentInfo {
    return Intl.message(
      'معلومات المحتوى',
      name: 'contentInfo',
      desc: '',
      args: [],
    );
  }

  /// `السؤال *`
  String get questionRequired {
    return Intl.message(
      'السؤال *',
      name: 'questionRequired',
      desc: '',
      args: [],
    );
  }

  /// `الإجابة (اختيارية)`
  String get answerOptional {
    return Intl.message(
      'الإجابة (اختيارية)',
      name: 'answerOptional',
      desc: '',
      args: [],
    );
  }

  /// `اكتب السؤال هنا...`
  String get writeQuestionHere {
    return Intl.message(
      'اكتب السؤال هنا...',
      name: 'writeQuestionHere',
      desc: '',
      args: [],
    );
  }

  /// `اكتب الإجابة هنا (اختيارية)...`
  String get writeAnswerHere {
    return Intl.message(
      'اكتب الإجابة هنا (اختيارية)...',
      name: 'writeAnswerHere',
      desc: '',
      args: [],
    );
  }

  /// `يرجى إدخال السؤال`
  String get pleaseEnterQuestion {
    return Intl.message(
      'يرجى إدخال السؤال',
      name: 'pleaseEnterQuestion',
      desc: '',
      args: [],
    );
  }

  /// `يجب أن يكون السؤال أكثر من 10 أحرف`
  String get questionTooShort {
    return Intl.message(
      'يجب أن يكون السؤال أكثر من 10 أحرف',
      name: 'questionTooShort',
      desc: '',
      args: [],
    );
  }

  /// `إنشاء السؤال`
  String get createQuestion {
    return Intl.message(
      'إنشاء السؤال',
      name: 'createQuestion',
      desc: '',
      args: [],
    );
  }

  /// `جاري الإنشاء...`
  String get creating {
    return Intl.message(
      'جاري الإنشاء...',
      name: 'creating',
      desc: '',
      args: [],
    );
  }

  /// `تم إنشاء السؤال بنجاح`
  String get questionCreatedSuccessfully {
    return Intl.message(
      'تم إنشاء السؤال بنجاح',
      name: 'questionCreatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `خطأ في إنشاء السؤال`
  String get errorCreatingQuestion {
    return Intl.message(
      'خطأ في إنشاء السؤال',
      name: 'errorCreatingQuestion',
      desc: '',
      args: [],
    );
  }

  /// `نصائح:`
  String get tips {
    return Intl.message('نصائح:', name: 'tips', desc: '', args: []);
  }

  /// `اكتب سؤالاً واضحاً ومفهوماً`
  String get writeClearQuestion {
    return Intl.message(
      'اكتب سؤالاً واضحاً ومفهوماً',
      name: 'writeClearQuestion',
      desc: '',
      args: [],
    );
  }

  /// `يمكنك ترك الإجابة فارغة وإضافتها لاحقاً`
  String get answerCanBeEmpty {
    return Intl.message(
      'يمكنك ترك الإجابة فارغة وإضافتها لاحقاً',
      name: 'answerCanBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `يمكن للذكاء الاصطناعي توليد إجابة تلقائياً`
  String get aiCanGenerateAnswer {
    return Intl.message(
      'يمكن للذكاء الاصطناعي توليد إجابة تلقائياً',
      name: 'aiCanGenerateAnswer',
      desc: '',
      args: [],
    );
  }

  /// `لا توجد أسئلة متاحة`
  String get noQuestionsAvailable {
    return Intl.message(
      'لا توجد أسئلة متاحة',
      name: 'noQuestionsAvailable',
      desc: '',
      args: [],
    );
  }

  /// `اضغط على زر + لإضافة سؤال جديد`
  String get clickPlusToAdd {
    return Intl.message(
      'اضغط على زر + لإضافة سؤال جديد',
      name: 'clickPlusToAdd',
      desc: '',
      args: [],
    );
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
