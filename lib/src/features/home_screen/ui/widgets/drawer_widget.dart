import 'package:dawraq/generated/assets.gen.dart';
import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/dialog/show_base_dialog.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/features/about_us/ui/about_us_screen.dart';
import 'package:dawraq/src/features/about_us/ui/model_767_screen.dart';
import 'package:dawraq/src/features/auth/providers/auth.providers.dart';
import 'package:dawraq/src/features/home_screen/ui/widgets/contact_us.screen.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class DrawerWidget extends ConsumerWidget {
  const DrawerWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // leading: Padding(
    //   padding: const EdgeInsets.only(
    //     bottom: AppSpaces.mediumPadding,
    //     right: AppSpaces.mediumPadding,
    //   ),
    //   child: InkWell(
    //     onTap: () {
    //       showBaseDialog(
    //         context,
    //         title: context.tr.contactUs,
    //         contentWidget: const SizedBox(
    //           width: 800,
    //           child: ContactUsDialog(),
    //         ),
    //         withoutConfirm: true,
    //       );
    //     },
    //     child: const CircleAvatar(
    //       radius: 20,
    //       backgroundColor: ColorManager.grey,
    //       child: Icon(
    //         Icons.contact_support,
    //       ),
    //     ),
    //   ),
    // ),
    // actions: [
    //   Padding(
    //     padding: const EdgeInsets.only(
    //       bottom: AppSpaces.mediumPadding,
    //       left: AppSpaces.mediumPadding,
    //     ),
    //     child: InkWell(
    //       onTap: () {
    //         final authController =
    //             ref.read(authControllerChangeNotifierProvider(context));
    //
    //         showBaseDialog(
    //           context,
    //           title: context.tr.logout,
    //           content: context.tr.areYouSureYouWantToLogout,
    //           isDelete: true,
    //           onConfirm: () {
    //             authController.logout();
    //           },
    //         );
    //       },
    //       child: const CircleAvatar(
    //         radius: 20,
    //         backgroundColor: ColorManager.primaryColor,
    //         child: Icon(
    //           Icons.logout,
    //         ),
    //       ),
    //     ),
    //   ),
    // ],
    return Drawer(
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: DrawerHeader(
                padding: EdgeInsets.zero,
                decoration:
                    const BoxDecoration(color: ColorManager.primaryColor),
                child: Assets.images.logo.image(
                  fit: BoxFit.cover,
                )),
          ),

          // * Contact Us
          ListTile(
            leading: const CircleAvatar(
              radius: 20,
              backgroundColor: Color(0xFF283b6c),
              child: Icon(
                Icons.phone,
              ),
            ),
            title: Text(context.tr.contactUs),
            onTap: () {
              context.to(const ContactUsScreen());
            },
          ),

          AppGaps.mediumGap,

          // * about us
          ListTile(
            leading: const CircleAvatar(
              radius: 20,
              backgroundColor: ColorManager.studentPageColor,
              child: Icon(
                Icons.info,
              ),
            ),
            title: Text(context.tr.aboutUs),
            onTap: () {
              context.to(const AboutUsScreen());
            },
          ),

          AppGaps.mediumGap,
          ListTile(
            leading: const CircleAvatar(
              radius: 20,
              backgroundColor: ColorManager.teacherPageColor,
              child: Icon(
                CupertinoIcons.command,
              ),
            ),
            title: const Text('Model 767'),
            onTap: () {
              context.to(const Model767Screen());
            },
          ),

          const Spacer(),

          // * Logout
          ListTile(
            leading: const CircleAvatar(
              radius: 20,
              backgroundColor: ColorManager.errorColor,
              child: Icon(
                Icons.logout,
              ),
            ),
            title: Text(context.tr.logout),
            onTap: () {
              final authController =
                  ref.read(authControllerChangeNotifierProvider(context));

              showBaseDialog(
                context,
                title: context.tr.logout,
                content: context.tr.areYouSureYouWantToLogout,
                isDelete: true,
                onConfirm: () {
                  authController.logout();
                },
              );
            },
          ),

          AppGaps.largeGap,
        ],
      ),
    );
  }
}
