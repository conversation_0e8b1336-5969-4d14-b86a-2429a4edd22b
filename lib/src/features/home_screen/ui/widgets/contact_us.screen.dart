import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/services/media/controller/media_controller.dart';
import 'package:dawraq/src/core/services/media/ui/media_picker.widget.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
import 'package:dawraq/src/features/auth/providers/auth.providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ContactUsScreen extends HookConsumerWidget {
  const ContactUsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nameController =
        useTextEditingController(text: UserModelHelper.currentUser().name);
    final phoneController = useTextEditingController();
    final noteController = useTextEditingController();

    final schoolNumberController = useTextEditingController(
        text: UserModelHelper.currentUser().schoolNumber);
    final schoolNameController = useTextEditingController();
    final jobController = useTextEditingController();
    final countryController = useTextEditingController();

    final mediaPickerController = ref.watch(mediaPickerControllerProvider);

    final authController =
        ref.watch(authControllerChangeNotifierProvider(context));

    final filesPaths = mediaPickerController.filesPaths;

    final formKey = useMemoized(() => GlobalKey<FormState>());

    return MobileDesignWidget(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          title: Text(
            context.tr.contactUs,
            style: context.title,
          ),
        ),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: AppSpaces.mediumPadding,
              vertical: AppSpaces.largePadding),
          child: Button(
            loadingWidget: const Center(
              child: LoadingWidget(),
            ),
            radius: AppRadius.smallRadius,
            label: context.tr.confirm,
            isWhiteText: true,
            isLoading: authController.isLoading,
            onPressed: () async {
              if (!formKey.currentState!.validate()) return;

              final data = {
                'name': nameController.text,
                'phone': phoneController.text,
                'message': noteController.text,
                'school_id': schoolNumberController.text,
                'school_name': schoolNameController.text,
                'job': jobController.text,
                'town': countryController.text,
              };

              await authController.sendContactMessage(
                data: data,
                filesPaths: filesPaths,
              );

              ref.read(mediaPickerControllerProvider.notifier).clearFiles();
            },
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  AppGaps.smallGap,
                  BaseTextField(
                    hint: '${context.tr.enter} ${context.tr.name}',
                    label: context.tr.name,
                    controller: nameController,
                  ),
                  AppGaps.mediumGap,
                  BaseTextField(
                    hint: '${context.tr.enter} ${context.tr.phone}',
                    label: context.tr.phone,
                    textInputType: TextInputType.phone,
                    controller: phoneController,
                    validator: (value) {
                      if (value!.isEmpty) {
                        return context.tr.required;
                      }
                      if (value.length < 10) {
                        return context.tr.phoneIsInvalid;
                      }
                      return null;
                    },
                  ),
                  AppGaps.mediumGap,
                  BaseTextField(
                    hint: '${context.tr.enter} ${context.tr.schoolNumber}',
                    label: context.tr.schoolNumber,
                    controller: schoolNumberController,
                  ),
                  AppGaps.mediumGap,
                  BaseTextField(
                    hint: '${context.tr.enter} ${context.tr.schoolName}',
                    label: context.tr.schoolName,
                    controller: schoolNameController,
                  ),
                  AppGaps.mediumGap,
                  BaseTextField(
                    hint: '${context.tr.enter} ${context.tr.job}',
                    label: context.tr.job,
                    controller: jobController,
                  ),
                  AppGaps.mediumGap,
                  BaseTextField(
                    hint: '${context.tr.enter} ${context.tr.country}',
                    label: context.tr.country,
                    controller: countryController,
                  ),
                  AppGaps.mediumGap,
                  BaseTextField(
                    hint: context.tr.note,
                    maxLines: 3,
                    controller: noteController,
                  ),
                  AppGaps.mediumGap,
                  const BaseMediaPickerWidget(),
                  AppGaps.largeGap,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
