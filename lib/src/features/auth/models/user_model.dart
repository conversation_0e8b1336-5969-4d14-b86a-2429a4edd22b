import 'package:equatable/equatable.dart';

class AIPermissions extends Equatable {
  final bool canViewAnalysis;
  final bool canViewQuestions;
  final bool canAddQuestions;
  final bool canEditQuestions;
  final bool canDeleteQuestions;
  final bool canGenerateQuestions;
  final bool canGenerateAnswers;
  final int questionsLimit;

  const AIPermissions({
    this.canViewAnalysis = false,
    this.canViewQuestions = false,
    this.canAddQuestions = false,
    this.canEditQuestions = false,
    this.canDeleteQuestions = false,
    this.canGenerateQuestions = false,
    this.canGenerateAnswers = false,
    this.questionsLimit = 0,
  });

  factory AIPermissions.fromJson(Map<String, dynamic>? json) {
    if (json == null) return const AIPermissions();
    return AIPermissions(
      canViewAnalysis: json['can_view_analysis'] ?? false,
      canViewQuestions: json['can_view_questions'] ?? false,
      canAddQuestions: json['can_add_questions'] ?? false,
      canEditQuestions: json['can_edit_questions'] ?? false,
      canDeleteQuestions: json['can_delete_questions'] ?? false,
      canGenerateQuestions: json['can_generate_questions'] ?? false,
      canGenerateAnswers: json['can_generate_answers'] ?? false,
      questionsLimit: json['questions_limit'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'can_view_analysis': canViewAnalysis,
      'can_view_questions': canViewQuestions,
      'can_add_questions': canAddQuestions,
      'can_edit_questions': canEditQuestions,
      'can_delete_questions': canDeleteQuestions,
      'can_generate_questions': canGenerateQuestions,
      'can_generate_answers': canGenerateAnswers,
      'questions_limit': questionsLimit,
    };
  }

  @override
  List<Object?> get props => [
        canViewAnalysis,
        canViewQuestions,
        canAddQuestions,
        canEditQuestions,
        canDeleteQuestions,
        canGenerateQuestions,
        canGenerateAnswers,
        questionsLimit,
      ];
}

class UserModel extends Equatable {
  final int? id;
  final String name;
  final String schoolID;
  final String password;
  final bool isTeacher;
  final String schoolNumber;
  final AIPermissions aiPermissions;

  const UserModel({
    this.id,
    this.name = '',
    this.schoolID = '',
    this.password = '',
    this.schoolNumber = '',
    this.isTeacher = false,
    this.aiPermissions = const AIPermissions(),
  });

  factory UserModel.empty() => const UserModel();

  // * From Json
  factory UserModel.fromJson(Map<String, dynamic>? json, {String? relation}) {
    if (json == null) return UserModel.empty();
    return UserModel(
      id: json['id'],
      name: json['name'] ?? '',
      schoolID: json['email'] ?? '',
      schoolNumber: json['username'] ?? '',
      isTeacher: json['type']?.toString().toLowerCase() == 'teacher',
      aiPermissions: AIPermissions.fromJson(json['ai_permissions']),
    );
  }

  // * To Json
  Map<String, dynamic> toJson() {
    return {
      'type': 'username',
      'credential': schoolID,
      'password': password,
      'fcm_token': 'fcmToken',
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        schoolID,
        password,
        isTeacher,
        aiPermissions,
      ];
}
