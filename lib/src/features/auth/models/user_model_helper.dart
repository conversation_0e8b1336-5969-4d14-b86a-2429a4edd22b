import 'package:dawraq/src/features/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

extension UserModelHelper on UserModel {
  // * Current User
  static UserModel currentUser() {
    final loggedUser = GetStorageService.getLocalData(
      key: LocalKeys.user,
    );

    return UserModel.fromJson(loggedUser);
  }

  // * Is Logged
  static bool isLogged() {
    final isLoggedIn = GetStorageService.hasData(key: LocalKeys.token);

    return isLoggedIn;
  }

  static int currentUserId() {
    final currentUser = UserModelHelper.currentUser();

    return currentUser.id ?? 0;
  }

  // * Is Current User
  static bool isMyData(
    int? id,
  ) {
    final currentUser = UserModelHelper.currentUser();

    return currentUser.id == id;
  }

  // * AI Permissions Helpers
  static bool canViewAnalysis() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.canViewAnalysis;
  }

  static bool canViewQuestions() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.canViewQuestions;
  }

  static bool canAddQuestions() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.canAddQuestions;
  }

  static bool canEditQuestions() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.canEditQuestions;
  }

  static bool canDeleteQuestions() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.canDeleteQuestions;
  }

  static bool canGenerateQuestions() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.canGenerateQuestions;
  }

  static bool canGenerateAnswers() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.canGenerateAnswers;
  }

  static int getQuestionsLimit() {
    final currentUser = UserModelHelper.currentUser();
    return currentUser.aiPermissions.questionsLimit;
  }
}
