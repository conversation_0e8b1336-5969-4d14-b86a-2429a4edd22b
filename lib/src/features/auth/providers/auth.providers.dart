// * Change Notifier ========================================
import 'package:dawraq/src/core/providers/network_api_service_provider.dart';
import 'package:dawraq/src/features/auth/controllers/auth.controller.dart';
import 'package:dawraq/src/features/auth/repos/auth.repo.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

// * Repo Provider ========================================
final authRepoProvider = Provider<AuthRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return AuthRepo(networkApiService);
});


// * Change Notifier ========================================
final authControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<AuthController, BuildContext>(
  (ref, context) {
    final authRepo = ref.watch(authRepoProvider);

    return AuthController(
      context,
      authRepo: authRepo,
    );
  },
);

// * Provider ========================================
final authControllerProvider = Provider.family<AuthController, BuildContext>(
  (ref, context) {
    final authRepo = ref.watch(authRepoProvider);

    return AuthController(
      context,
      authRepo: authRepo,
    );
  },
);

// * Get User Profile Future Provider ========================================
// final getUserProfile =
//     FutureProvider.family<UserModel, (BuildContext, int? userId)>(
//   (ref, params) {
//     final context = params.$1;
//     final userId = params.$2;
//
//     final authController = ref.watch(authControllerProvider(context));
//
//     return authController.getProfile(userId: userId);
//   },
// );
