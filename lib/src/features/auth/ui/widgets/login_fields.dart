import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginFields extends StatelessWidget {
  final TextEditingController emailController;
  final TextEditingController passwordController;

  const LoginFields(
      {super.key,
      required this.emailController,
      required this.passwordController});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //! Username
        BaseTextField(
          suffixIcon: const Icon(
            Icons.school,
            color: ColorManager.lightGrey2,
          ),
          label: context.tr.schoolID,
          controller: emailController,
        ),

        AppGaps.largeGap,

        //! Password
        _PasswordField(
          passwordController: passwordController,
        ),
      ],
    );
  }
}

class _Password<PERSON>ield extends HookWidget {
  final TextEditingController passwordController;

  const _PasswordField({
    required this.passwordController,
  });

  @override
  Widget build(BuildContext context) {
    final isObscure = useState(true);

    final iconButton = IconButton(
      onPressed: () => isObscure.value = !isObscure.value,
      icon: Icon(
        isObscure.value
            ? CupertinoIcons.eye_fill
            : CupertinoIcons.eye_slash_fill,
        color: ColorManager.lightGrey2,
      ),
    );

    return BaseTextField(
      suffixIcon: iconButton,
      label: context.tr.password,
      controller: passwordController,
      isObscure: isObscure.value,
    );
  }
}
