import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:dawraq/src/features/auth/providers/auth.providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginButton extends ConsumerWidget {
  final TextEditingController emailController;
  final TextEditingController passwordController;

  const LoginButton({
    super.key,
    required this.emailController,
    required this.passwordController,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController =
        ref.watch(authControllerChangeNotifierProvider(context));

    return Button(
      isLoading: authController.isLoading,
      loadingWidget: const LoadingWidget(),
      label: context.tr.signIn,
      textColor: Colors.white,
      onPressed: () async {
        final email = emailController.text;
        final password = passwordController.text;

        await authController.login(
          email: email,
          password: password,
        );
      },
    );
  }
}
