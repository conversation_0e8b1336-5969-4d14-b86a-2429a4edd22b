import 'dart:io';

import 'package:dawraq/generated/assets.gen.dart';
import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/core/shared_widgets/dialog/show_base_dialog.dart';
import 'package:dawraq/src/features/auth/ui/widgets/login_button.dart';
import 'package:dawraq/src/features/auth/ui/widgets/login_fields.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/app_consts.dart';

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final emailController = useTextEditingController(
        text: kDebugMode ? AppConsts.testSchoolID : '');
    final passwordController =
        useTextEditingController(text: kDebugMode ? AppConsts.testPass : '');

    return MobileDesignWidget(
      child: WillPopScope(
        onWillPop: () async {
          showBaseDialog(
            context,
            title: context.tr.exitApp,
            content: context.tr.areYouSureYouWantToExitApp,
            isDelete: true,
            onConfirm: () {
              exit(0);
            },
          );
          return false;
        },
        child: Scaffold(
          body: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: Column(
                  children: [
                    AppGaps.mediumGap,

                    //! Login Image
                    Assets.images.loginLogo.image(
                      fit: BoxFit.cover,
                      width: 360.h,
                    ),

                    AppGaps.xLargeGap,

                    //! Login Fields
                    LoginFields(
                      emailController: emailController,
                      passwordController: passwordController,
                    ),

                    AppGaps.xLargeGap,

                    LoginButton(
                      emailController: emailController,
                      passwordController: passwordController,
                    ),

                    AppGaps.xLargeGap,

                    //! Login Button

                    // AppGaps.xLargeGap,
                    //
                    // OutlinedButton(
                    //     onPressed: () {
                    //       context.to(const Model767Screen());
                    //     },
                    //     child: const Text(
                    //       'Model 767',
                    //     )).sized(
                    //   height: 50.h,
                    //   width: 150.w,
                    // ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
