import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/extensions/string_extensions.dart';
import 'package:dawraq/src/features/auth/models/user_model.dart';
import 'package:dawraq/src/features/auth/repos/auth.repo.dart';
import 'package:dawraq/src/features/auth/ui/login_screen.dart';
import 'package:dawraq/src/features/home_screen/ui/home_screen.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthController extends BaseController {
  final BuildContext context;
  final AuthRepo authRepo;

  AuthController(
    this.context, {
    required this.authRepo,
  });

  // * Login ================================
  Future<UserModel?> login({
    required String email,
    required String password,
  }) async {
    return await baseFunction(
      context,
      () async {
        final user = UserModel(
          schoolID: email,
          password: password,
        );

        final userData = await authRepo.login(user: user);

        _navigateToHome();

        return userData;
      },
      onError: (error) {
        context.showBarMessage(error.toString().translateErrorMessage,
            isError: true);
      },
    );
  }

  // * Send Contact Message ================================
  Future<void> sendContactMessage({
    required Map<String, dynamic> data,
    required List<PlatformFile> filesPaths,
  }) async {
    return await baseFunction(
      context,
      () async {
        await authRepo.sendContactMessage(data: data, filesPaths: filesPaths);
      },
      additionalFunction: (_) {
        context.to(const HomeScreen());

        context.showBarMessage(context.tr.messageSentSuccessfully);
      },
      onError: (error) {
        context.showBarMessage(error.toString().translateErrorMessage,
            isError: true);
      },
    );
  }

  Future<void> logout() async {
    await authRepo.logout();
    _navigateToLogin();
  }

  void _navigateToHome() {
    context.toReplacement(const HomeScreen());
  }

  void _navigateToLogin() {
    context.toReplacement(const LoginScreen());
  }
}
