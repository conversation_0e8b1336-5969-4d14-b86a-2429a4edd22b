import 'dart:async';

import 'package:dawraq/generated/assets.gen.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/features/auth/ui/login_screen.dart';
import 'package:dawraq/src/features/home_screen/ui/home_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class SplashScreen extends HookWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    useEffect(() {
      Timer(const Duration(milliseconds: 5200), () {
        final isLoggedIn = GetStorageService.hasData(key: LocalKeys.token);

        final screen = isLoggedIn ? const HomeScreen() : const LoginScreen();

        context.toReplacement(screen);
      });

      return null;
    }, []);

    return MobileDesignWidget(
      child: Scaffold(
        body: Center(
          child: Assets.animated.splash.image(
            fit: BoxFit.cover,
            height: context.height,
            width: context.width,
          ),
        ),
      ),
    );
  }
}
