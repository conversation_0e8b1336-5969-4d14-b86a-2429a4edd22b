import 'package:equatable/equatable.dart';

class AboutUsModel extends Equatable {
  final int? id;
  final String title;
  final String content;
  final List<String> media;

  const AboutUsModel({
    this.id,
    this.title = '',
    this.content = '',
    this.media = const [],
  });

  factory AboutUsModel.fromJson(Map<String, dynamic> json) {
    return AboutUsModel(
      id: json['id'],
      title: json['title'] != null ? json['title']['ar'] : '',
      content: json['content'] != null ? json['content']['ar'] : '',
      media: List<String>.from(
          ((json['media'] as List?) ?? []).map((x) => x['url'])),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        media,
      ];
}
