import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/about_us_model.dart';
import '../repos/about_us.repo.dart';

class AboutUsController extends BaseController {
  final BuildContext context;
  final AboutUsRepo aboutUsRepo;

  AboutUsController(
    this.context, {
    required this.aboutUsRepo,
  });

  // * Get AboutUs ================================
  Future<AboutUsModel> getAboutUS() async {
    return await baseFunction(context, () async {
      final data = await aboutUsRepo.getAboutUS();

      return data;
    });
  }

  // * Get Model 767
  Future<AboutUsModel> getModel767() async {
    return await baseFunction(context, () async {
      final data = await aboutUsRepo.getModel767();

      return data;
    });
  }
}
