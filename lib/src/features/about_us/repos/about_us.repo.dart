import 'package:dawraq/src/core/data/remote/response/api_end_points.dart';
import 'package:dawraq/src/features/about_us/models/about_us_model.dart';
import 'package:xr_helper/xr_helper.dart';

class AboutUsRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  AboutUsRepo(this._networkApiService);

  // * Get About Us ================================
  Future<AboutUsModel> getAboutUS() async {
    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(
          ApiEndPoints.aboutUs,
        );

        final data = response['data'];

        final aboutUs = AboutUsModel.fromJson(data);

        return aboutUs;
      },
    );
  }

  // * Get Model 767
  Future<AboutUsModel> getModel767() async {
    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(
          ApiEndPoints.model767,
        );

        final data = response['data'];

        final aboutUs = AboutUsModel.fromJson(data);

        return aboutUs;
      },
    );
  }
}
