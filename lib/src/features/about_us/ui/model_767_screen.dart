import 'package:dawraq/src/core/extensions/riverpod_extensions.dart';
import 'package:dawraq/src/core/extensions/string_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/features/about_us/providers/about_us.providers.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/widgets/base_details.widget.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/widgets/tab_bars/video_tab_bar.widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared_widgets/loading/loading_widget.dart';

class Model767Screen extends HookConsumerWidget {
  const Model767Screen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabIndex = useState(0);
    final getModel767Future = ref.watch(model767FutureProvider(context));

    return MobileDesignWidget(
      child: getModel767Future.get(
        loading: () => const Material(
          child: Center(
            child: LoadingWidget(),
          ),
        ),
        data: (model767) {
          final videos = model767.media;

          return Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              title: Text(
                model767.title,
                style: context.title,
              ),
            ),
            body: Padding(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              child: Column(
                children: [
                  if (videos.isNotEmpty) ...[
                    if (videos.length == 1) ...[
                      if (videos.first.isVideo)
                        ViewMediaWidget(
                          mediaURL: videos.first,
                          fit: BoxFit.contain,
                          fromModel767: true,
                        ),
                    ] else ...[
                      VideoTabBarWidget(
                        tabIndex: tabIndex,
                        videos: videos,
                      ),
                      AppGaps.mediumGap,
                      IndexedStack(
                        index: tabIndex.value,
                        children: videos
                            .map((video) => ViewMediaWidget(
                                  mediaURL: video,
                                  fit: BoxFit.contain,
                                  fromModel767: true,
                                ))
                            .toList(),
                      ),
                    ],
                    AppGaps.largeGap,
                  ],
                  Expanded(
                    child: SingleChildScrollView(
                      child: HtmlWidget(
                        model767.content,
                        onLoadingBuilder: (context, element, loadingProgress) =>
                            const Center(
                          child: LoadingWidget(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
