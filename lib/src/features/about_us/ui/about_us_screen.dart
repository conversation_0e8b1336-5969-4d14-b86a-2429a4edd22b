import 'package:dawraq/src/core/extensions/riverpod_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/features/about_us/providers/about_us.providers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/shared_widgets/loading/loading_widget.dart';

class AboutUsScreen extends ConsumerWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final getAboutUSFuture = ref.watch(aboutUsFutureProvider(context));

    return MobileDesignWidget(
      child: getAboutUSFuture.get(
        loading: () => const Material(
          child: Center(
            child: LoadingWidget(),
          ),
        ),
        data: (aboutUs) {
          return Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              surfaceTintColor: Colors.transparent,
              title: Text(
                aboutUs.title,
                style: context.title,
              ),
            ),
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              child: HtmlWidget(
                aboutUs.content,
                onLoadingBuilder: (context, element, loadingProgress) =>
                    const Center(
                  child: LoadingWidget(),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
