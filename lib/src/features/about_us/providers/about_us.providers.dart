import 'package:dawraq/src/core/providers/network_api_service_provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../controllers/about_us.controller.dart';
import '../models/about_us_model.dart';
import '../repos/about_us.repo.dart';

// * Repo Provider ========================================

final aboutUsRepoProvider = Provider<AboutUsRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return AboutUsRepo(networkApiService);
});

// * Change Notifier ========================================
final aboutUsControllerNotifierProvider =
    ChangeNotifierProvider.family<AboutUsController, BuildContext>(
  (
    ref,
    context,
  ) {
    final aboutUsRepo = ref.watch(aboutUsRepoProvider);

    return AboutUsController(
      context,
      aboutUsRepo: aboutUsRepo,
    );
  },
);

// * Provider ========================================
final aboutUsControllerProvider =
    Provider.family<AboutUsController, BuildContext>(
  (
    ref,
    context,
  ) {
    final aboutUsRepo = ref.watch(aboutUsRepoProvider);

    return AboutUsController(
      context,
      aboutUsRepo: aboutUsRepo,
    );
  },
);

final aboutUsFutureProvider = FutureProvider.family<AboutUsModel, BuildContext>(
  (ref, context) {
    final aboutUsController = ref.watch(
      aboutUsControllerProvider(context),
    );

    return aboutUsController.getAboutUS();
  },
);

// * Model 767 Future Provider ========================================
final model767FutureProvider =
    FutureProvider.family<AboutUsModel, BuildContext>(
  (ref, context) {
    final aboutUsController = ref.watch(
      aboutUsControllerProvider(context),
    );

    return aboutUsController.getModel767();
  },
);
