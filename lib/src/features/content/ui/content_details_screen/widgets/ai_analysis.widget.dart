import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:xr_helper/xr_helper.dart';

class AIAnalysisWidget extends StatelessWidget {
  final AIAnalysis? aiAnalysis;
  final Color color;

  const AIAnalysisWidget({
    super.key,
    required this.aiAnalysis,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    if (aiAnalysis == null) {
      return Center(
        child: Text(
          'لا يوجد تحليل متاح',
          style: AppTextStyles.title,
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      children: [
        Row(
          children: [
            CircleAvatar(
              radius: 5,
              backgroundColor: color,
            ),
            AppGaps.smallGap,
            Text(
              'التحليل بالذكاء الاصطناعي',
              style: AppTextStyles.title,
            ),
          ],
        ),
        AppGaps.mediumGap,
        if (aiAnalysis!.createdAt != null) ...[
          Text(
            'تاريخ التحليل: ${aiAnalysis!.createdAt!.date}',
            style: AppTextStyles.subTitle.copyWith(
              color: ColorManager.darkGrey,
            ),
          ),
          AppGaps.mediumGap,
        ],
        HtmlWidget(
          aiAnalysis!.analysis,
          onLoadingBuilder: (context, element, loadingProgress) => const Center(
            child: LoadingWidget(),
          ),
        ),
      ],
    );
  }
}
