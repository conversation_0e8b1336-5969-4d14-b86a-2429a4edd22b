import 'package:better_player_plus/better_player_plus.dart';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:xr_helper/xr_helper.dart';

// security
// remove teacher tab from web
// handle http security issue
class VideoWidget extends StatefulWidget {
  final String videoUrl;
  final BoxFit fit;

  const VideoWidget(
      {super.key, required this.videoUrl, this.fit = BoxFit.fill});

  @override
  VideoWidgetState createState() => VideoWidgetState();
}

class VideoWidgetState extends State<VideoWidget>
    with AutomaticKeepAliveClientMixin {
  late VideoPlayerController _videoPlayerController;
  late ChewieController _chewieController;

  @override
  void initState() {
    super.initState();

    Log.w('VideoURL: ${widget.videoUrl}');

    _videoPlayerController = VideoPlayerController.network(widget.videoUrl);

    _chewieController = ChewieController(
      videoPlayerController: _videoPlayerController,
      aspectRatio: 16 / 9,
      autoPlay: false,
      looping: false,
      allowFullScreen: true,
      allowMuting: true,
      showControls: true,
      customControls: null,

      // onEnterFullscreen: () {
      //   // Adjust layout for fullscreen
      //   if (kIsWeb) {
      //     html.document.documentElement?.requestFullscreen();
      //   }
      // },
      // onExitFullscreen: () {
      //   // Revert layout constraints
      //   if (kIsWeb) {
      //     html.document.exitFullscreen();
      //   }
      // },
      placeholder: Container(
        color: Colors.grey,
      ),
      autoInitialize: true,
    );
  }

  @override
  void dispose() {
    _videoPlayerController.dispose();
    _chewieController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Directionality(
      textDirection: TextDirection.ltr,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: Chewie(
            controller: _chewieController,
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class MobileVideoWidget extends StatefulWidget {
  final String videoUrl;
  final BoxFit fit;

  const MobileVideoWidget(
      {super.key, required this.videoUrl, this.fit = BoxFit.fill});

  @override
  MobileVideoWidgetState createState() => MobileVideoWidgetState();
}

class MobileVideoWidgetState extends State<MobileVideoWidget>
    with AutomaticKeepAliveClientMixin {
  late BetterPlayerController _betterPlayerController;

  @override
  void initState() {
    super.initState();

    Log.w('VideoURL: ${widget.videoUrl}');

    BetterPlayerDataSource dataSource = BetterPlayerDataSource(
      BetterPlayerDataSourceType.network,
      widget.videoUrl,
    );

    BetterPlayerControlsConfiguration controlsConfiguration =
        const BetterPlayerControlsConfiguration(
      enableQualities: false,
      enableSubtitles: false,
      enableAudioTracks: false,
    );

    _betterPlayerController = BetterPlayerController(
      BetterPlayerConfiguration(
        looping: false,
        aspectRatio: 16 / 9,
        fit: widget.fit,
        autoPlay: false,
        fullScreenByDefault: false,
        controlsConfiguration: controlsConfiguration,
      ),
      betterPlayerDataSource: dataSource,
    );
  }

  @override
  void dispose() {
    _betterPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ClipRRect(
      borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
      child: AspectRatio(
        aspectRatio: 16 / 9,
        child: BetterPlayer(
          controller: _betterPlayerController,
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
