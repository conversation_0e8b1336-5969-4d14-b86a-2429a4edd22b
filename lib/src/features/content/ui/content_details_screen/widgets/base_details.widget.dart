import 'dart:io';

import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/extensions/string_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/widgets/tab_bars/video_tab_bar.widget.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/widgets/video.widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseDetailsWidget extends HookWidget {
  final String content;
  final List<String> videos;
  final Color color;

  const BaseDetailsWidget({
    super.key,
    required this.content,
    required this.videos,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final tabIndex = useState(UserModelHelper.currentUser().isTeacher ? 0 : 2);

    return ListView(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      children: [
        if (videos.isNotEmpty) ...[
          if (videos.length == 1) ...[
            if (videos.first.isVideo)
              ViewMediaWidget(
                mediaURL: videos.first,
              ),
          ] else ...[
            VideoTabBarWidget(
              tabIndex: tabIndex,
              videos: videos,
            ),
            AppGaps.mediumGap,
            IndexedStack(
              index: tabIndex.value,
              children: videos
                  .map((video) => ViewMediaWidget(mediaURL: video))
                  .toList(),
            ),
          ],
          AppGaps.largeGap,
        ],
        Row(
          children: [
            CircleAvatar(
              radius: 5,
              backgroundColor: color,
            ),
            AppGaps.smallGap,
            Text(
              context.tr.explanation,
              style: AppTextStyles.title,
            ),
          ],
        ),
        AppGaps.mediumGap,
        HtmlWidget(
          content,
          onLoadingBuilder: (context, element, loadingProgress) => const Center(
            child: LoadingWidget(),
          ),
        ),
      ],
    );
  }
}

class ViewMediaWidget extends StatelessWidget {
  final String mediaURL;
  final BoxFit fit;
  final bool fromModel767;

  const ViewMediaWidget({
    super.key,
    required this.mediaURL,
    this.fit = BoxFit.fill,
    this.fromModel767 = false,
  });

  @override
  Widget build(BuildContext context) {
    final media =
        '$mediaURL?token=${GetStorageService.getLocalData(key: LocalKeys.token)}';

    Log.w('mediaURL: $media');

    if (mediaURL.isVideo) {
      if (kIsWeb) {
        return VideoWidget(
          videoUrl: media,
          fit: fit,
        );
      } else {
        return MobileVideoWidget(
          videoUrl: media,
          fit: fit,
        );
      }
    } else {
      return ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
        child: BaseCachedImage(
          media,
          height: 200,
          width: double.infinity,
          fit: BoxFit.cover,
        ),
      );
    }
  }
}
