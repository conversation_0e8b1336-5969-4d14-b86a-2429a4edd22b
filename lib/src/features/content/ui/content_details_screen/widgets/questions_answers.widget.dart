import 'package:dawraq/src/core/data/remote/response/api_end_points.dart';
import 'package:dawraq/src/core/providers/network_api_service_provider.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/content_details.screen.dart';
import 'package:dawraq/src/features/content/ui/create_question_screen/create_question.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

class QuestionsAnswersWidget extends HookConsumerWidget {
  final List<QuestionAnswer> questionsAnswers;
  final Color color;
  final ContentModel content;

  const QuestionsAnswersWidget({
    super.key,
    required this.questionsAnswers,
    required this.color,
    required this.content,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkService = ref.watch(networkServiceProvider);
    final loadingStates = useState<Map<int, bool>>({});

    void navigateBackToQuestionsTab() {
      final questionsTabIndex =
          ContentDetailsScreen.getQuestionsTabIndex(content);
      context.toReplacement(ContentDetailsScreen(
        mainContent: content,
        initialTabIndex: questionsTabIndex,
      ));
    }

    Future<void> navigateToCreateQuestion() async {
      final result = await Navigator.push<bool>(
        context,
        MaterialPageRoute(
          builder: (context) => CreateQuestionScreen(content: content),
        ),
      );

      if (result == true) {
        // Navigate back to content details with questions tab selected
        navigateBackToQuestionsTab();
      }
    }

    Future<void> generateAiAnswer(int questionId) async {
      try {
        loadingStates.value = {...loadingStates.value, questionId: true};

        await networkService.postResponse(
          '${ApiEndPoints.addAiAnswer}/$questionId',
          body: {},
          // body: {'question_id': questionId},
        );

        // Navigate back to refresh the questions tab
        navigateBackToQuestionsTab();
        // Show success message
        context.showBarMessage('تم توليد الإجابة بنجاح');
      } catch (e) {
        context.showBarMessage('خطأ في توليد الإجابة', isError: true);
      } finally {
        loadingStates.value = {...loadingStates.value, questionId: false};
      }
    }

    Future<void> updateAnswer(int questionId, String currentAnswer) async {
      final answerController = TextEditingController(text: currentAnswer);

      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تعديل الإجابة'),
          content: TextField(
            controller: answerController,
            maxLines: 5,
            decoration: const InputDecoration(
              hintText: 'اكتب الإجابة هنا...',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, answerController.text),
              child: const Text(
                'حفظ',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );

      if (result != null && result.isNotEmpty) {
        try {
          loadingStates.value = {...loadingStates.value, questionId: true};

          await networkService.postResponse(
            ApiEndPoints.updateQuestion,
            body: {
              'question_id': questionId,
              'answer': result,
            },
          );

          // Navigate back to refresh the questions tab
          navigateBackToQuestionsTab();
          context.showBarMessage('تم حفظ الإجابة بنجاح');
        } catch (e) {
          context.showBarMessage('خطأ في حفظ الإجابة', isError: true);
        } finally {
          loadingStates.value = {...loadingStates.value, questionId: false};
        }
      }
    }

    Widget buildContent() {
      if (questionsAnswers.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.quiz_outlined,
                size: 64,
                color: ColorManager.darkGrey.withOpacity(0.5),
              ),
              AppGaps.mediumGap,
              Text(
                'لا توجد أسئلة متاحة',
                style: AppTextStyles.title.copyWith(
                  color: ColorManager.darkGrey,
                ),
              ),
              AppGaps.smallGap,
              if (UserModelHelper.canAddQuestions())
                Text(
                  'اضغط على زر + لإضافة سؤال جديد',
                  style: AppTextStyles.subTitle.copyWith(
                    color: ColorManager.darkGrey,
                  ),
                ),
            ],
          ),
        );
      }

      return ListView(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 5,
                backgroundColor: color,
              ),
              AppGaps.smallGap,
              Text(
                'الأسئلة والأجوبة',
                style: AppTextStyles.title,
              ),
            ],
          ),
          AppGaps.mediumGap,
          ...questionsAnswers.map((qa) => _buildQuestionCard(
                context,
                qa,
                loadingStates.value[qa.id] ?? false,
                generateAiAnswer,
                updateAnswer,
              )),
        ],
      );
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: buildContent(),
      floatingActionButton: UserModelHelper.canAddQuestions()
          ? FloatingActionButton(
              onPressed: navigateToCreateQuestion,
              backgroundColor: ColorManager.primaryColor,
              foregroundColor: Colors.white,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildQuestionCard(
    BuildContext context,
    QuestionAnswer qa,
    bool isLoading,
    Function(int) onGenerateAiAnswer,
    Function(int, String) onUpdateAnswer,
  ) {
    final isMyQuestion = UserModelHelper.isMyData(qa.userId);

    return Card(
      margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Text(
            qa.question,
            style: AppTextStyles.subTitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppGaps.smallGap,
              Row(
                children: [
                  _buildInfoChip('الصعوبة: ${qa.difficultyLabel}',
                      ColorManager.primaryColor),
                  AppGaps.smallGap,
                  _buildInfoChip(
                      qa.isAiGeneratedLabel,
                      qa.isAiGenerated
                          ? ColorManager.successColor
                          : ColorManager.darkGrey),
                ],
              ),
              if (qa.createdAtHuman != null) ...[
                AppGaps.xSmallGap,
                Text(
                  'تاريخ الإنشاء: ${qa.createdAtHuman!.date}',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: ColorManager.darkGrey,
                  ),
                ),
              ],
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI Answer button
              if (UserModelHelper.canGenerateAnswers() && qa.answer.isEmpty)
                IconButton(
                  onPressed:
                      isLoading ? null : () => onGenerateAiAnswer(qa.id!),
                  icon: isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.auto_awesome,
                          color: ColorManager.primaryColor),
                  tooltip: 'توليد إجابة بالذكاء الاصطناعي',
                ),
              // Edit button for own questions
              if (isMyQuestion && UserModelHelper.canEditQuestions())
                IconButton(
                  onPressed: isLoading
                      ? null
                      : () => onUpdateAnswer(qa.id!, qa.answer),
                  icon: const Icon(Icons.edit, color: ColorManager.darkGrey),
                  tooltip: 'تعديل الإجابة',
                ),
              const Icon(Icons.expand_more),
            ],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Answer Section
                  if (qa.answer.isNotEmpty) ...[
                    Row(
                      children: [
                        const Icon(Icons.question_answer,
                            color: ColorManager.successColor, size: 20),
                        AppGaps.smallGap,
                        Text(
                          'الإجابة:',
                          style: AppTextStyles.subTitle.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ColorManager.successColor,
                          ),
                        ),
                      ],
                    ),
                    AppGaps.mediumGap,
                    HtmlWidget(
                      qa.answer,
                      onLoadingBuilder: (context, element, loadingProgress) =>
                          const Center(child: LoadingWidget()),
                    ),
                  ] else ...[
                    Text(
                      'لا توجد إجابة متاحة',
                      style: AppTextStyles.labelSmall.copyWith(
                        color: ColorManager.darkGrey,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],

                  // Attachment Section
                  if (qa.attachment.isNotEmpty) ...[
                    if (qa.answer.isNotEmpty) AppGaps.largeGap,
                    Row(
                      children: [
                        const Icon(Icons.attach_file,
                            color: ColorManager.primaryColor, size: 20),
                        AppGaps.smallGap,
                        Text(
                          'المرفق:',
                          style: AppTextStyles.subTitle.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    AppGaps.smallGap,
                    _buildAttachmentWidget(qa.attachment),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentWidget(String attachmentUrl) {
    // Extract filename from URL
    String fileName = attachmentUrl.split('/').last;

    // Get file extension to determine icon
    String extension = fileName.split('.').last.toLowerCase();
    IconData fileIcon;
    Color iconColor;

    switch (extension) {
      case 'pdf':
        fileIcon = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case 'doc':
      case 'docx':
        fileIcon = Icons.description;
        iconColor = Colors.blue;
        break;
      case 'xls':
      case 'xlsx':
        fileIcon = Icons.table_chart;
        iconColor = Colors.green;
        break;
      case 'ppt':
      case 'pptx':
        fileIcon = Icons.slideshow;
        iconColor = Colors.orange;
        break;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        fileIcon = Icons.image;
        iconColor = Colors.purple;
        break;
      case 'mp4':
      case 'avi':
      case 'mov':
        fileIcon = Icons.video_file;
        iconColor = Colors.indigo;
        break;
      case 'mp3':
      case 'wav':
        fileIcon = Icons.audio_file;
        iconColor = Colors.teal;
        break;
      default:
        fileIcon = Icons.insert_drive_file;
        iconColor = ColorManager.darkGrey;
    }

    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: ColorManager.primaryColor.withOpacity(0.05),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(
          color: ColorManager.primaryColor.withOpacity(0.2),
        ),
      ),
      child: InkWell(
        onTap: () async {
          try {
            final Uri url = Uri.parse(attachmentUrl);
            if (await canLaunchUrl(url)) {
              await launchUrl(url, mode: LaunchMode.externalApplication);
            } else {
              throw 'Could not launch $attachmentUrl';
            }
          } catch (e) {
            // Handle error - could show a snackbar or dialog
            debugPrint('Error launching URL: $e');
          }
        },
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        child: Row(
          children: [
            Icon(
              fileIcon,
              color: iconColor,
              size: 24,
            ),
            AppGaps.smallGap,
            Expanded(
              child: Text(
                fileName,
                style: AppTextStyles.subTitle.copyWith(
                  color: ColorManager.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Icon(
              Icons.download,
              color: ColorManager.primaryColor,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.smallPadding,
        vertical: AppSpaces.xSmallPadding,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: AppTextStyles.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
