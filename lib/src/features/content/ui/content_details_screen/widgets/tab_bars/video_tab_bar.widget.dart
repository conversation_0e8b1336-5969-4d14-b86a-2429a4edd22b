import 'package:dawraq/src/core/shared_widgets/base_tab_bar/base_tab_bar.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class VideoTabBarWidget extends StatelessWidget {
  final ValueNotifier<int> tabIndex;
  final List<String> videos;

  const VideoTabBarWidget({
    super.key,
    required this.tabIndex,
    required this.videos,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(
        horizontal: AppSpaces.largePadding,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          AppRadius.xxLargeRadius,
        ),
        border: Border.all(
          color: Colors.grey,
          width: 0.5,
        ),
      ),
      child: BaseTabBar(
        isScrollable: videos.length > 3,
        dividerHeight: 0,
        indicatorColor: ColorManager.teacherTabColor,
        tabs: videos.indexed.map((e) {
          return Tab(
            text: 'فيديو ${e.$1 + 1}',
          );
        }).toList(),
        onTap: (index) {
          tabIndex.value = index;
        },
        initialTabIndex: 0,
      ),
    );
  }
}
