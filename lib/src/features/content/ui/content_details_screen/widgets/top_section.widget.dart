import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class TopSectionWidget extends StatelessWidget {
  final String title;

  const TopSectionWidget({
    super.key,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        AppGaps.smallGap,
        IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            size: 24,
            color: Colors.white,
          ),
          onPressed: () {
            // for (final controller in videoControllers.values) {
            //   controller.pause();
            // }

            context.back();
          },
        ),
        AppGaps.mediumGap,
        Text(
          title,
          style: AppTextStyles.whiteSubHeadLine,
        ),
      ],
    ).paddingOnly(
      top: AppSpaces.smallPadding,
    );
  }
}
