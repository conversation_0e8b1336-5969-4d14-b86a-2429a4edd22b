import 'dart:convert';

import 'package:dawraq/src/core/data/remote/response/api_end_points.dart';
import 'package:dawraq/src/core/providers/network_api_service_provider.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/content_details.screen.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class CreateQuestionScreen extends HookConsumerWidget {
  final ContentModel content;

  const CreateQuestionScreen({
    super.key,
    required this.content,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkService = ref.watch(networkServiceProvider);
    final questionController = useTextEditingController();
    final answerController = useTextEditingController();
    final isLoading = useState(false);
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final selectedFile = useState<PlatformFile?>(null);
    final filePath = useState<String>('');

    Future<void> pickFile() async {
      try {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.any,
          allowMultiple: false,
        );

        if (result != null && result.files.isNotEmpty) {
          selectedFile.value = result.files.first;

          // For web, use bytes; for mobile, use path
          if (kIsWeb) {
            filePath.value = selectedFile.value!.name;
          } else {
            filePath.value = selectedFile.value!.path ?? '';
          }
        }
      } catch (e) {
        context.showBarMessage('خطأ في اختيار الملف', isError: true);
      }
    }

    void removeFile() {
      selectedFile.value = null;
      filePath.value = '';
    }

    Future<void> createQuestion() async {
      if (!formKey.currentState!.validate()) return;

      try {
        isLoading.value = true;

        await networkService.postResponse(
          ApiEndPoints.createQuestion,
          fieldName: 'attachment',
          filePaths: selectedFile.value != null ? [selectedFile.value!] : [],
          body: {
            'content_id': content.id,
            'question': questionController.text.trim(),
            'answer': answerController.text.trim(),
          },
        );

        // Navigate back to content details with questions tab selected
        final questionsTabIndex =
            ContentDetailsScreen.getQuestionsTabIndex(content);
        context.back();
        context.toReplacement(ContentDetailsScreen(
          mainContent: content,
          initialTabIndex: questionsTabIndex,
        ));
        context.showBarMessage('تم إنشاء السؤال بنجاح');
      } catch (e) {
        context.showBarMessage(jsonDecode(e.toString())['message'].toString(),
            isError: true);
      } finally {
        isLoading.value = false;
      }
    }

    return MobileDesignWidget(
      child: Scaffold(
        backgroundColor: ColorManager.backgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          title: Text(
            'إنشاء سؤال جديد',
            style: AppTextStyles.title,
          ),
          leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios),
          ),
        ),
        body: Form(
          key: formKey,
          child: ListView(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            children: [
              // Question Field
              Text(
                'السؤال *',
                style: AppTextStyles.subTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.smallGap,
              TextFormField(
                controller: questionController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'اكتب السؤال هنا...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    borderSide:
                        const BorderSide(color: ColorManager.primaryColor),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال السؤال';
                  }
                  if (value.trim().length < 10) {
                    return 'يجب أن يكون السؤال أكثر من 10 أحرف';
                  }
                  return null;
                },
              ),

              AppGaps.largeGap,

              // Answer Field (Optional)
              Text(
                'الإجابة (اختيارية)',
                style: AppTextStyles.subTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.smallGap,
              TextFormField(
                controller: answerController,
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'اكتب الإجابة هنا (اختيارية)...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    borderSide:
                        const BorderSide(color: ColorManager.primaryColor),
                  ),
                ),
              ),

              AppGaps.largeGap,

              // File Attachment Section
              Text(
                'إرفاق ملف (اختياري)',
                style: AppTextStyles.subTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.smallGap,

              // File picker button or selected file display
              if (selectedFile.value == null) ...[
                Container(
                  width: double.infinity,
                  height: 60,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: ColorManager.primaryColor.withOpacity(0.3),
                      style: BorderStyle.solid,
                    ),
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    color: ColorManager.primaryColor.withOpacity(0.05),
                  ),
                  child: InkWell(
                    onTap: pickFile,
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.attach_file,
                          color: ColorManager.primaryColor,
                          size: 24,
                        ),
                        AppGaps.smallGap,
                        Text(
                          'اختر ملف للإرفاق',
                          style: AppTextStyles.subTitle.copyWith(
                            color: ColorManager.primaryColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ] else ...[
                // Selected file display
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: ColorManager.successColor.withOpacity(0.3),
                    ),
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    color: ColorManager.successColor.withOpacity(0.05),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.insert_drive_file,
                        color: ColorManager.successColor,
                        size: 24,
                      ),
                      AppGaps.smallGap,
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              selectedFile.value!.name,
                              style: AppTextStyles.subTitle.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (selectedFile.value!.size > 0) ...[
                              AppGaps.xSmallGap,
                              Text(
                                '${(selectedFile.value!.size / 1024 / 1024).toStringAsFixed(2)} MB',
                                style: AppTextStyles.labelSmall.copyWith(
                                  color: ColorManager.darkGrey,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: removeFile,
                        icon: const Icon(
                          Icons.close,
                          color: ColorManager.errorColor,
                        ),
                        tooltip: 'إزالة الملف',
                      ),
                    ],
                  ),
                ),
              ],

              AppGaps.xLargeGap,

              // Create Button
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: isLoading.value ? null : createQuestion,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(AppRadius.smallRadius),
                    ),
                  ),
                  child: isLoading.value
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 10),
                            Text('جاري الإنشاء...'),
                          ],
                        )
                      : Text(
                          'إنشاء السؤال',
                          style: AppTextStyles.subTitle.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),

              AppGaps.mediumGap,

              // Info Text
              Container(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  border: Border.all(
                    color: ColorManager.primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.lightbulb_outline,
                          color: ColorManager.primaryColor,
                          size: 20,
                        ),
                        AppGaps.smallGap,
                        Text(
                          'نصائح:',
                          style: AppTextStyles.labelLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    AppGaps.smallGap,
                    Text(
                      '• اكتب سؤالاً واضحاً ومفهوماً\n'
                      '• يمكنك ترك الإجابة فارغة وإضافتها لاحقاً\n'
                      '• يمكن للذكاء الاصطناعي توليد إجابة تلقائياً',
                      style: AppTextStyles.labelMedium.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
