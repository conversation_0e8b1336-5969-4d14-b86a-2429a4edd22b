import 'dart:developer';

import 'package:dawraq/src/features/content/models/base_created_date.model.dart';
import 'package:equatable/equatable.dart';

class AIAnalysis extends Equatable {
  final int? id;
  final String analysis;
  final BaseCreatedDateModel? createdAt;

  const AIAnalysis({
    this.id,
    this.analysis = '',
    this.createdAt,
  });

  factory AIAnalysis.fromJson(Map<String, dynamic>? json) {
    if (json == null) return const AIAnalysis();
    return AIAnalysis(
      id: json['id'],
      analysis: json['analysis'] ?? '',
      createdAt: json['created_at'] != null
          ? BaseCreatedDateModel.fromJson(json['created_at'])
          : null,
    );
  }

  @override
  List<Object?> get props => [id, analysis, createdAt];
}

class QuestionAnswer extends Equatable {
  final int? id;
  final int? contentId;
  final String question;
  final String answer;
  final String difficulty;
  final String difficultyLabel;
  final int? userId;
  final String type;
  final bool isAiGenerated;
  final String isAiGeneratedLabel;
  final String createdAt;
  final String updatedAt;
  final BaseCreatedDateModel? createdAtHuman;
  final BaseCreatedDateModel? updatedAtHuman;
  final String attachment;

  const QuestionAnswer({
    this.id,
    this.contentId,
    this.question = '',
    this.answer = '',
    this.difficulty = '',
    this.difficultyLabel = '',
    this.userId,
    this.type = '',
    this.isAiGenerated = false,
    this.isAiGeneratedLabel = '',
    this.createdAt = '',
    this.updatedAt = '',
    this.createdAtHuman,
    this.updatedAtHuman,
    this.attachment = '',
  });

  factory QuestionAnswer.fromJson(Map<String, dynamic>? json) {
    if (json == null) return const QuestionAnswer();
    log('DDDD ${json['files']}');

    return QuestionAnswer(
      id: json['id'],
      contentId: json['content_id'],
      question: json['question'] ?? '',
      answer: json['answer'] ?? '',
      difficulty: json['difficulty'] ?? '',
      difficultyLabel: json['difficulty_label'] ?? '',
      userId: json['user_id'],
      type: json['type'] ?? '',
      isAiGenerated: json['is_ai_generated'] ?? false,
      isAiGeneratedLabel: json['is_ai_generated_label'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      createdAtHuman: json['created_at_human'] != null
          ? BaseCreatedDateModel.fromJson(json['created_at_human'])
          : null,
      updatedAtHuman: json['updated_at_human'] != null
          ? BaseCreatedDateModel.fromJson(json['updated_at_human'])
          : null,
      attachment: json['files'] != null && json['files'].isNotEmpty
          ? (json['files'] as List<dynamic>).first['url']
          : '',
    );
  }

  static List<QuestionAnswer> fromJsonList(List<dynamic>? jsonList) {
    if (jsonList == null) return [];
    return jsonList.map((json) => QuestionAnswer.fromJson(json)).toList();
  }

  @override
  List<Object?> get props => [
        id,
        contentId,
        question,
        answer,
        difficulty,
        difficultyLabel,
        userId,
        type,
        isAiGenerated,
        isAiGeneratedLabel,
        createdAt,
        updatedAt,
        createdAtHuman,
        updatedAtHuman,
        attachment,
      ];
}

//getContentResponseDataToList
List<ContentModel> getContentResponseDataToList(dynamic response) {
  final List<ContentModel> contentList = [];
  final List<dynamic> data = response['categories'];

  for (final item in data) {
    final content = ContentModel.fromJson(item);
    contentList.add(content);
  }

  return contentList;
}

class ContentModel extends Equatable {
  final int? id;
  final String title;
  final String description;
  final String image;
  final BaseCreatedDateModel? createdAt;

  //? For details
  final String contentClass;
  final String contentTeacher;
  final String contentStudent;

  final List<String> classMedia;
  final List<String> teacherMedia;
  final List<String> studentMedia;

  //? AI Features
  final AIAnalysis? aiAnalysis;
  final List<QuestionAnswer> questionsAnswers;
  final bool hasAiAnalysis;

  const ContentModel({
    this.id,
    this.title = '',
    this.description = '',
    this.image = '',
    this.createdAt,
    this.contentClass = '',
    this.contentTeacher = '',
    this.contentStudent = '',
    this.classMedia = const [],
    this.teacherMedia = const [],
    this.studentMedia = const [],
    this.aiAnalysis,
    this.questionsAnswers = const [],
    this.hasAiAnalysis = false,
  });

  factory ContentModel.fromJson(Map<String, dynamic> json) {
    return ContentModel(
      id: json['id'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      image: json['media'] ?? '',
      createdAt: json['created_at'] != null
          ? BaseCreatedDateModel.fromJson(json['created_at'])
          : null,

      //? For details
      contentClass: json['content_class'] ?? '',
      contentTeacher: json['content_teacher'] ?? '',
      contentStudent: json['content_student'] ?? '',
      classMedia: List<String>.from(
          ((json['classMedia'] as List?) ?? []).map((x) => x['url'])),
      teacherMedia: List<String>.from(
          ((json['teacherMedia'] as List?) ?? []).map((x) => x['url'])),
      studentMedia: List<String>.from(
          ((json['studentMedia'] as List?) ?? []).map((x) => x['url'])),

      //? AI Features
      aiAnalysis: AIAnalysis.fromJson(json['ai_analysis']),
      questionsAnswers: QuestionAnswer.fromJsonList(json['questions_answers']),
      hasAiAnalysis: json['has_ai_analysis'] ?? false,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        image,
        createdAt,
        contentClass,
        contentTeacher,
        contentStudent,
        classMedia,
        teacherMedia,
        studentMedia,
        aiAnalysis,
        questionsAnswers,
        hasAiAnalysis,
      ];
}
