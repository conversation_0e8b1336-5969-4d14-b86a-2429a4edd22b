import 'package:equatable/equatable.dart';

class BaseCreatedDateModel extends Equatable {
  final String human;
  final String date;

  const BaseCreatedDateModel({
    this.human = '',
    this.date = '',
  });

  // * From Json ================================
  factory BaseCreatedDateModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return BaseCreatedDateModel.empty();
    }
    return BaseCreatedDateModel(
      human: json['human'],
      date: json['date'],
    );
  }

  // * Empty Constructor
  factory BaseCreatedDateModel.empty() {
    return const BaseCreatedDateModel();
  }

  @override
  List<Object?> get props => [
        human,
        date,
      ];
}
