import 'package:dawraq/src/core/providers/network_api_service_provider.dart';
import 'package:dawraq/src/features/content/controllers/content.controller.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/repos/content.repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Repo Provider ========================================

final contentRepoProvider = Provider<ContentRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ContentRepo(networkApiService);
});

// * Change Notifier ========================================
final contentControllerNotifierProvider =
    ChangeNotifierProvider.family<ContentController, BuildContext>(
  (
    ref,
    context,
  ) {
    final contentRepo = ref.watch(contentRepoProvider);

    return ContentController(
      context,
      contentRepo: contentRepo,
    );
  },
);

// * Provider ========================================
final contentControllerProvider =
    Provider.family<ContentController, BuildContext>(
  (
    ref,
    context,
  ) {
    final contentRepo = ref.watch(contentRepoProvider);

    return ContentController(
      context,
      contentRepo: contentRepo,
    );
  },
);

// * Content Future Provider ========================================
// final mainContentFutureProvider = FutureProvider.family<
//     (bool isActive, List<ContentModel> content),
//     (BuildContext, String searchValue)>(
//   (ref, params) {
//     final contentController = ref.watch(
//       contentControllerProvider(params.$1),
//     );
//
//     return contentController.getMainContent(
//       searchValue: params.$2,
//     );
//   },
// );
final mainContentFutureProvider = FutureProvider.family<
    (bool isActive, List<ContentModel> content),
    (BuildContext, String searchValue, int perPage)>(
      (ref, params) {
    final contentController = ref.watch(
      contentControllerProvider(params.$1),
    );

    return contentController.getMainContent(
      searchValue: params.$2,
      perPage: params.$3,
    );
  },
);

// * Content Details Future Provider ========================================
final contentDetailsFutureProvider =
    FutureProvider.family<ContentModel, (BuildContext, int)>(
  (ref, params) {
    final contentController = ref.watch(
      contentControllerProvider(params.$1),
    );

    return contentController.getContentDetails(params.$2);
  },
);
