import 'package:dawraq/src/core/data/remote/response/api_end_points.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:flutter/foundation.dart';
import 'package:xr_helper/xr_helper.dart';

class ContentRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  ContentRepo(this._networkApiService);

  // * Get Content ================================
  Future<(bool isActive, List<ContentModel> content)> getMainContent({
    String? searchValue,
    int perPage = 10, // Default value
  }) async {
    return baseFunction(
          () async {
        final search = searchValue != null && searchValue.isNotEmpty
            ? '?search=$searchValue&perPage=$perPage'
            : '?perPage=$perPage';

        final response = await _networkApiService.getResponse(
          ApiEndPoints.mainContent + search,
        );

        final data = response['data'];

        final content = await compute(
          getContentResponseDataToList,
          data,
        );

        final isActive = data['schoolStatus'] != null
            ? data['schoolStatus']['status'] == 'active'
            : false;

        return (isActive, content);
      },
    );
  }
  // Future<(bool isActive, List<ContentModel> content)> getMainContent({
  //   String? searchValue,
  // }) async {
  //   return baseFunction(
  //     () async {
  //       final search = searchValue != null && searchValue.isNotEmpty
  //           ? '?search=$searchValue'
  //           : '';
  //
  //       final response = await _networkApiService.getResponse(
  //         ApiEndPoints.mainContent + search,
  //       );
  //
  //       final data = response['data'];
  //
  //       final content = await compute(
  //         getContentResponseDataToList,
  //         data,
  //       );
  //
  //       final isActive = data['schoolStatus'] != null
  //           ? data['schoolStatus']['status'] == 'active'
  //           : false;
  //
  //       return (isActive, content);
  //     },
  //   );
  // }

  // * Get Content Details ================================
  Future<ContentModel> getContentDetails(int id) async {
    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(
          ApiEndPoints.contentDetails(id),
        );

        final data = response['data'] is List
            ? response['data'].isEmpty
                ? null
                : response['data'].last
            : response['data'];

        final content = ContentModel.fromJson(data ?? {});

        return content;
      },
    );
  }
}
