import 'dart:convert';
import 'dart:developer';

import 'package:dawraq/src/core/consts/app_consts.dart';
import 'package:dawraq/src/core/services/app_settings/controller/settings_controller.dart';
import 'package:dawraq/src/core/theme/theme_manager.dart';
import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
import 'package:dawraq/src/features/splash_screen/ui/splash_screen.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:http/http.dart' as http;
import 'package:no_screenshot/no_screenshot.dart';
import 'package:xr_helper/xr_helper.dart';

const double webAppWidth = 700;

class BaseApp extends HookConsumerWidget {
  const BaseApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerProvider);
    final themeController = ref.watch(themeProvider);
    final textTheme = AppConsts.isEnglish(ref)
        ? GoogleFonts.poppinsTextTheme()
        : GoogleFonts.cairoTextTheme();

    // log('asfsafsa ${UserModelHelper.currentUser().isTeacher}');
    // log('asfsafsrrrra ${GetStorageService.getLocalData(key: 'user')}');

    final noScreenshot = useMemoized(() => NoScreenshot.instance);
    final appLifecycleState = useAppLifecycleState();

    final canOpenApp = useState<bool>(true);

    Future<DateTime> fetchUTCTime() async {
      final response = await http.get(Uri.parse(
          'https://www.timeapi.io/api/Time/current/zone?timeZone=UTC'));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return DateTime.parse(data['dateTime']);
      } else {
        return DateTime.now().toUtc();
        // throw Exception('Failed to fetch UTC time');
      }
    }

    Future canOpenTheApp() async {
      try {
        final utcNow = await fetchUTCTime(); // Get real UTC time

        final israelTime =
            utcNow.add(const Duration(hours: 2)); // Convert to UTC+2

        const startHour = 7;
        const endHour = 15;

        canOpenApp.value =
            israelTime.hour >= startHour && israelTime.hour < endHour;
      } catch (e) {
        Log.e("Error fetching time: $e");
      }
    }

    // bool canOpenTheApp() {
    //   final now = DateTime.now();
    //   final israelTime = now.toUtc().add(const Duration(hours: 2));
    //
    //   const startHour = 7;
    //   const endHour = 15;
    //
    //   if (israelTime.hour >= startHour && israelTime.hour < endHour) {
    //     return true;
    //   }
    //   return false;
    // }

    useEffect(() {
      void handleAppLifecycleState(AppLifecycleState state) {
        void toggleScreenShot() {
          noScreenshot.screenshotOff();
          // if (canOpenTheApp()) {
          //   noScreenshot.screenshotOn();
          // } else {
          // }
        }

        switch (state) {
          case AppLifecycleState.resumed:
            Log.w("App in resumed");
            toggleScreenShot();
            break;
          case AppLifecycleState.inactive:
            Log.w("App in inactive");
            toggleScreenShot();
            break;
          case AppLifecycleState.paused:
            Log.w("App in paused");
            toggleScreenShot();
            break;
          case AppLifecycleState.detached:
            Log.w("App in detached");
            break;
          case AppLifecycleState.hidden:
            Log.w("App in hidden");
            toggleScreenShot();
            break;
        }
      }

      if (!kIsWeb) {
        handleAppLifecycleState(appLifecycleState!);
      }

      return null;
    }, [appLifecycleState]);

    useEffect(() {
      if (kIsWeb && !kDebugMode) {
        canOpenTheApp();
      }

      return () {};
    }, []);

    return ScreenUtilInit(
      designSize: const Size(390, 844),
      builder: (_, child) => BaseMaterialApp(
        //? Localization
        locale: settingsController.locale,
        supportedLocales: AppConsts.supportedLocales,
        localizationsDelegates: AppConsts.localizationsDelegates,
        //? Theme
        themeMode: ThemeMode.light,
        theme: themeController.appTheme(
          textTheme: textTheme,
        ),
        //canOpenApp.value || kDebugMode || !kIsWeb
        //             ? const SplashScreen()
        //             : const CannotOpenAppWidget()
        home: ValueListenableBuilder<bool>(
          valueListenable: canOpenApp,
          builder: (context, value, child) {
            if (kIsWeb) {
              if (value) {
                return const SplashScreen();
              } else {
                return const CannotOpenAppWidget();
              }
            } else {
              return const SplashScreen();
            }
          },
        ),
        title: AppConsts.appName,
      ),
    );
  }
}

class CannotOpenAppWidget extends StatelessWidget {
  const CannotOpenAppWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircleAvatar(
              radius: 50,
              backgroundColor: Colors.red,
              child: Icon(
                Icons.error,
                size: 50,
                color: Colors.white,
              ),
            ),
            AppGaps.largeGap,
            Text(
              'عذراً، لا يمكنك فتح التطبيق الآن !',
              style: context.headLine.copyWith(color: Colors.red),
            ),
            AppGaps.mediumGap,
            Text('ساعات العمل من الساعة 7 صباحاً حتى الساعة 3 عصراً',
                style: context.labelLarge),
          ],
        ),
      ),
    );
  }
}

class CannotOpenFromWebMobile extends StatelessWidget {
  const CannotOpenFromWebMobile({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircleAvatar(
              radius: 50,
              backgroundColor: Colors.red,
              child: Icon(
                Icons.error,
                size: 50,
                color: Colors.white,
              ),
            ),
            AppGaps.largeGap,
            Text(
              'عذراً، لا يمكنك فتح التطبيق !',
              style: context.headLine.copyWith(color: Colors.red),
            ),
            AppGaps.mediumGap,
            Text('يمكنك فتح التطبيق من الكمبيوتر او اللابتوب فقط',
                style: context.labelLarge),
          ],
        ),
      ),
    );
  }
}

// import 'package:dawraq/src/core/consts/app_consts.dart';
// import 'package:dawraq/src/core/services/app_settings/controller/settings_controller.dart';
// import 'package:dawraq/src/core/theme/theme_manager.dart';
// import 'package:dawraq/src/features/splash_screen/ui/splash_screen.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:google_fonts/google_fonts.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:no_screenshot/no_screenshot.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class BaseApp extends HookConsumerWidget {
//   const BaseApp({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final settingsController = ref.watch(settingsControllerProvider);
//     final themeController = ref.watch(themeProvider);
//     final textTheme = AppConsts.isEnglish(ref)
//         ? GoogleFonts.poppinsTextTheme()
//         : GoogleFonts.cairoTextTheme();
//
//     final noScreenshot = useMemoized(() => NoScreenshot.instance);
//     final appLifecycleState = useAppLifecycleState();
//
//     useEffect(() {
//       void handleAppLifecycleState(AppLifecycleState state) {
//         switch (state) {
//           case AppLifecycleState.resumed:
//             Log.w("app in resumed");
//             noScreenshot.screenshotOff();
//             break;
//           case AppLifecycleState.inactive:
//             Log.w("app in inactive");
//             noScreenshot.screenshotOff();
//             break;
//           case AppLifecycleState.paused:
//             Log.w("app in paused");
//             noScreenshot.screenshotOff();
//             break;
//           case AppLifecycleState.detached:
//             Log.w("app in detached");
//             break;
//           case AppLifecycleState.hidden:
//             Log.w("app in hidden");
//             noScreenshot.screenshotOn();
//             break;
//         }
//       }
//
//       handleAppLifecycleState(appLifecycleState!);
//
//       return null;
//     }, [appLifecycleState]);

//     return ScreenUtilInit(
//       designSize: const Size(390, 844),
//       builder: (_, child) => BaseMaterialApp(
//         //? Localization
//         locale: settingsController.locale,
//         supportedLocales: AppConsts.supportedLocales,
//         localizationsDelegates: AppConsts.localizationsDelegates,
//         //? Theme
//         themeMode: ThemeMode.light,
//         theme: themeController.appTheme(
//           textTheme: textTheme,
//         ),
//         home: const SplashScreen(),
//         // isLoggedIn ? const HomeScreen() : const LoginScreen(),
//         title: AppConsts.appName,
//       ),
//     );
//   }
// }
