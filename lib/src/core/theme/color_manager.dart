import 'package:flutter/material.dart';

class ColorManager {
  static const backgroundColor = Color(0xFFFFFFFF);

  // static const backgroundColor = Color(0xFFFFFFFF);
  static const primaryColor = Color(0xFF009FE7);
  static const lightPrimaryColor = Color(0xFFFEC409);
  static const darkPrimaryColor = Color(0xFF283b6c);
  static const verifyColor = Color(0xFF005AFF);
  static const secondaryColor = Colors.black;

  // static final secondaryColor = Colors.blueGrey.shade300;
  static const bottomNavIconColor = Color(0xFF727272);

  static const buttonColor = secondaryColor;
  static const containerColor = Color(0xFFDDE1FF);

  // grey
  static const fieldColor = Color(0xFFE5E5E5);
  static const red = Colors.red;
  static const white = Color(0xFFFFFFFF);
  static const black = Color(0xFF000000);
  static const grey = Color(0xFFCBD5E1);
  static const lightGrey = Color(0xFFF9F8F8);
  static const lightGrey2 = Color(0xFFD9D9D9);
  static const highlightColor = Color(0xFFFFFFFF);
  static const shimmerBaseColor = Color(0xFFCECECE);
  static const cardColor = Color(0xFFEDEDED);
  static const darkGrey = Color(0xFF8B8B8B);
  static const darkBlue = Color(0xFF23292F);
  static const iconColor = Color(0xFF727272);

  static const successColor = Color(0xFF2ECC71);
  static const errorColor = Color(0xFFE74C3C);

  static const classTabColor = Color(0xFFF4C4C4);
  static const teacherTabColor = Color(0xFFBCEAFF);
  static const studentTabColor = Color(0xFFFDE7A0);

  static const classPageColor = Color(0xFFfcc0c0);
  static const teacherPageColor = Color(0xFF10a9ee);
  static const studentPageColor = Color(0xFFfec409);

  //! Gradients
  static const primaryGradient = LinearGradient(
    colors: [
      ColorManager.primaryColor,
      ColorManager.lightPrimaryColor,
    ],
  );
}
