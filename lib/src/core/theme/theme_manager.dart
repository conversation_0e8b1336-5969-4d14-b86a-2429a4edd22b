import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:xr_helper/xr_helper.dart';

import 'color_manager.dart';

final themeProvider = Provider<AppTheme>((ref) {
  return AppTheme();
});

class AppTheme {
  //? Out Line Border -------------------------------------
  OutlineInputBorder get _outLineBorder => OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        borderSide: const BorderSide(color: ColorManager.grey),
      );

  //? None Input Border -------------------------------------
  InputDecorationTheme get noneInputBorder => const InputDecorationTheme(
      enabledBorder:
          OutlineInputBorder(borderSide: BorderSide(color: ColorManager.black)),
      focusedBorder: InputBorder.none,
      errorBorder: InputBorder.none);

  //? App Bar Theme -------------------------------------
  get _appBarTheme => const AppBarTheme(
      backgroundColor: ColorManager.primaryColor,
      iconTheme: IconThemeData(color: Colors.white),
      titleTextStyle: TextStyle(
          color: Colors.white, fontWeight: FontWeight.bold, fontSize: 22));

  get colorScheme => const ColorScheme.light().copyWith(
        primary: ColorManager.primaryColor,
        secondary: ColorManager.secondaryColor,
      );

  //? Button Theme -------------------------------------
  get _buttonTheme => ElevatedButtonThemeData(
      style: ButtonStyle(
          shape: MaterialStatePropertyAll(RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppRadius.mediumRadius))),
          foregroundColor: const MaterialStatePropertyAll(Colors.white70),
          backgroundColor:
              const MaterialStatePropertyAll(ColorManager.primaryColor)));

  //? Input Decoration Theme -------------------------------------
  get _inputDecorationTheme => InputDecorationTheme(
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      enabledBorder: _outLineBorder,
      focusedBorder: _outLineBorder,
      errorBorder: _outLineBorder,
      focusedErrorBorder: _outLineBorder,
      disabledBorder: _outLineBorder,
      border: _outLineBorder,
      errorStyle: const TextStyle(color: ColorManager.errorColor),
      hintStyle: const TextStyle(color: ColorManager.grey));

  //? Bottom Navigation Bar Theme -------------------------------------
  get _bottomNavigationBar => const BottomNavigationBarThemeData(
        elevation: 20,
        showSelectedLabels: true,
        backgroundColor: ColorManager.primaryColor,
        selectedIconTheme: IconThemeData(color: ColorManager.black),
        unselectedIconTheme: IconThemeData(color: ColorManager.black),
        selectedItemColor: ColorManager.white,
        unselectedItemColor: ColorManager.iconColor,
      );

  //? List Tile Theme
  final _listTile = const ListTileThemeData(
    iconColor: ColorManager.black,
  );

  //? Floating Action Button Theme
  final _floatingActionButtonThemeData = const FloatingActionButtonThemeData(
    backgroundColor: ColorManager.primaryColor,
    foregroundColor: ColorManager.black,
  );

  //? Theme Data -------------------------------------
//? Theme Data -------------------------------------
  ThemeData appTheme({
    TextTheme? textTheme,
  }) =>
      ThemeData(
        scaffoldBackgroundColor: ColorManager.backgroundColor,
        useMaterial3: true,
        brightness: Brightness.light,

        primaryColor: ColorManager.primaryColor,
        textTheme: textTheme ?? GoogleFonts.workSansTextTheme(),
        //! AppBar Theme
        appBarTheme: _appBarTheme,

        //! Color Scheme
        colorScheme: colorScheme,
        iconButtonTheme: const IconButtonThemeData(
            style: ButtonStyle(
                iconColor: MaterialStatePropertyAll(ColorManager.black))),

        //! Button Theme
        elevatedButtonTheme: _buttonTheme,

        //! input decoration theme
        inputDecorationTheme: _inputDecorationTheme,

        //! Bottom Navigation Bar Theme
        bottomNavigationBarTheme: _bottomNavigationBar,

        //! Floating Action Button Theme
        floatingActionButtonTheme: _floatingActionButtonThemeData,

        //! List Tile Theme
        listTileTheme: _listTile,

        //! Icon Themes
        iconTheme: const IconThemeData(color: Colors.black),
        primaryIconTheme: const IconThemeData(color: Colors.black),
      );
}
