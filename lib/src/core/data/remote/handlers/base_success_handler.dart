// import 'package:flutter/material.dart';
// import 'package:xr_helper/xr_helper.dart';

// enum BaseDataActionType { add, update, delete }

// void baseOnSuccessHandler(BuildContext context,
//     {Widget? screen,
//     bool isBack = false,
//     String? message,
//     BaseDataActionType actionType = BaseDataActionType.add}) {
//   if (screen != null) {
//     context.toReplacement(screen);
//   }

//   if (isBack) {
//     context.back();
//   }

//   var msg;

//   if (message != null) {
//     msg = message;
//   } else {
//     msg = getMessageForActionType(context, actionType: actionType);
//   }

//   final isError = msg == context.tr.deletedSuccessfully;

//   context.showBarMessage(
//     msg,
//     isError: actionType == BaseDataActionType.delete,
//   );
// }

// // get message for action type
// String getMessageForActionType(BuildContext context,
//     {required BaseDataActionType actionType}) {
//   switch (actionType) {
//     case BaseDataActionType.add:
//       return context.tr.addedSuccessfully;
//     case BaseDataActionType.update:
//       return context.tr.updatedSuccessfully;
//     case BaseDataActionType.delete:
//       return context.tr.deletedSuccessfully;
//     default:
//       return context.tr.addedSuccessfully;
//   }
// }
