import 'package:collection/collection.dart';

enum BaseMediaType {
  image,
  video,
  audio,
  file,
}

extension BaseMediaTypeExtensions on BaseMediaType {
  bool get isVideo => this == BaseMediaType.video;

  bool get isImage => this == BaseMediaType.image;

  bool get isAudio => this == BaseMediaType.audio;

  bool get isFile => this == BaseMediaType.file;
}

class BaseMediaModel {
  final int? id;
  final int? order;
  final String? url;
  final BaseMediaType type;

  BaseMediaModel({
    this.id,
    required this.url,
    this.order,
    this.type = BaseMediaType.image,
  });

  factory BaseMediaModel.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return BaseMediaModel.empty();
    }

    return BaseMediaModel(
      id: json['id'],
      order: json['order'],
      url: json['url'],
      type: BaseMediaType.values.firstWhereOrNull(
            (e) => e.toString().split('.').last == json['type'],
          ) ??
          BaseMediaType.image,
    );
  }

  static List<BaseMediaModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => BaseMediaModel.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order': order,
      'url': url,
    };
  }

  factory BaseMediaModel.empty() => BaseMediaModel(url: '');
}
