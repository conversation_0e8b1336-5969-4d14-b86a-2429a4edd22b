import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

Future<void> showBaseDialog(
  BuildContext context, {
  required String title,
  String? content,
  Widget? contentWidget,
  Function? onConfirm,
  Function? onCancel,
  bool isDelete = false,
  bool withoutConfirm = false,
}) async {
  await showGeneralDialog(
    context: context,
    barrierDismissible: true,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    barrierColor: Colors.black45,
    transitionDuration: const Duration(milliseconds: 200),
    pageBuilder: (BuildContext buildContext, Animation animation,
        Animation secondaryAnimation) {
      return AlertDialog(
        title: Text(title, style: AppTextStyles.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (content != null) Text(content, style: AppTextStyles.body),
            if (contentWidget != null) contentWidget,
          ],
        ),
        actions: [
          if (!withoutConfirm)
            TextButton(
              onPressed: () {
                onCancel == null ? context.back() : onCancel();
              },
              child: Text(
                  onConfirm == null ? context.tr.submit : context.tr.cancel,
                  style: AppTextStyles.labelLarge),
            ),
          if (onConfirm != null)
            TextButton(
              onPressed: () {
                onConfirm();
              },
              child: Text(
                context.tr.confirm,
                style: isDelete
                    ? AppTextStyles.redLabelLarge
                    : AppTextStyles.greenLabelLarge,
              ),
            ),
        ],
      );
    },
    transitionBuilder: (
      BuildContext context,
      animation,
      secondaryAnimation,
      child,
    ) {
      return ScaleTransition(
        scale: CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ),
        child: child,
      );
    },
  );
}
