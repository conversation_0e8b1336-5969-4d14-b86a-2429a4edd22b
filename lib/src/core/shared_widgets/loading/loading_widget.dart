import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: LoadingAnimationWidget.twoRotatingArc(
          color: ColorManager.primaryColor, size: 45),
    );
    // return Center(
    //     child: Assets.animated.loading.lottie(
    //   height: 120,
    //   fit: BoxFit.cover,
    // ));
  }
}
