import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

class MainShimmerLoading extends StatelessWidget {
  final Widget child;
  const MainShimmerLoading({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
        highlightColor: ColorManager.highlightColor,
        baseColor: ColorManager.shimmerBaseColor,
        child: child);
  }
}
