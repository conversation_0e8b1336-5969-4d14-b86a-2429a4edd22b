import 'package:dawraq/src/core/shared_widgets/loading/loading_widgets/home_loading/main_shimmer_loading.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class CardsLoading extends StatelessWidget {
  const CardsLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return BaseList(
        data: const [1, 2, 3],
        itemBuilder: (index, data) {
          return MainShimmerLoading(
            child: Container(
              height: kIsWeb ? 220.h : 200.h,
              decoration: const BoxDecoration(
                  color: ColorManager.primaryColor,
                  borderRadius: BorderRadius.all(Radius.circular(15))),
            ),
          );
        });
  }
}
