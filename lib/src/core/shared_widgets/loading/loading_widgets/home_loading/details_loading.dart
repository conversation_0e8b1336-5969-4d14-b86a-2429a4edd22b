import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widgets/home_loading/main_shimmer_loading.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/widgets/tab_bars/tab_bar.widget.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/widgets/top_section.widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class DetailsLoading extends StatelessWidget {
  final String title;

  const DetailsLoading({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Material(
      child: Padding(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: MainShimmerLoading(
          child: Column(
            children: [
              IgnorePointer(
                ignoring: true,
                child: MainShimmerLoading(
                  child: TopSectionWidget(
                    title: title,
                  ),
                ),
              ),
              AppGaps.largeGap,
              MainShimmerLoading(
                  child: DetailsTabBarWidget(
                tabIndex: ValueNotifier<int>(0),
                isLoading: ValueNotifier<bool>(false),
              )),
              AppGaps.largeGap,
              Container(
                height: kIsWeb ? 250.h : 200.h,
                decoration: const BoxDecoration(
                    color: ColorManager.primaryColor,
                    borderRadius: BorderRadius.all(Radius.circular(15))),
              ),
              AppGaps.largeGap,
              MainShimmerLoading(
                child: Row(
                  children: [
                    const CircleAvatar(
                      radius: 5,
                      backgroundColor: ColorManager.primaryColor,
                    ),
                    AppGaps.smallGap,
                    Text(
                      context.tr.explanation,
                      style: AppTextStyles.title,
                    ),
                  ],
                ),
              ),
              AppGaps.mediumGap,
              MainShimmerLoading(
                child: Container(
                  height: 400.h,
                  decoration: const BoxDecoration(
                      color: ColorManager.primaryColor,
                      borderRadius: BorderRadius.all(Radius.circular(15))),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
