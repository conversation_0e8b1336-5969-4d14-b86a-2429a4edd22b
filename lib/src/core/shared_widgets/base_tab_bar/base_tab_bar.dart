import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseTabBar extends StatelessWidget {
  final List<Tab> tabs;
  final void Function(int) onTap;
  final bool isScrollable;

  final int initialTabIndex;

  final double? dividerHeight;
  final Color? indicatorColor;

  const BaseTabBar({
    required this.tabs,
    required this.onTap,
    this.isScrollable = false,
    this.initialTabIndex = 0,
    this.dividerHeight,
    this.indicatorColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: tabs.length,
      initialIndex: initialTabIndex,
      child: TabBar(
        tabs: tabs,
        dividerHeight: dividerHeight,
        labelStyle: AppTextStyles.whiteSubTitle.copyWith(
          fontWeight: FontWeight.bold,
          fontFamily: GoogleFonts.cairo().fontFamily,
        ),
        tabAlignment: isScrollable ? TabAlignment.start : null,
        isScrollable: isScrollable,
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.smallPadding,
            vertical: AppSpaces.xSmallPadding),
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.xxLargeRadius),
          color: indicatorColor,
        ),
        onTap: onTap,
      ),
    );
  }
}
