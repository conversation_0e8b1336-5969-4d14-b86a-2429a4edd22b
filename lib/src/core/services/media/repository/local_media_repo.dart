import 'package:file_picker/file_picker.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xr_helper/xr_helper.dart';

final localMediaRepoProvider = Provider<LocalMediaRepo>((ref) {
  return LocalMediaRepo();
});

class LocalMediaRepo {
  Future<FilePickerResult?> pickFiles({
    bool imageUpload = true,
    bool uploadMultiple = true,
    bool useCamera = false,
  }) async {
    try {
      await _getPermission();

      if (useCamera) {
        final picker = ImagePicker();
        final pickedFile = await picker.pickImage(source: ImageSource.camera);

        if (pickedFile != null) {
          final file = PlatformFile(
            name: pickedFile.path.split('/').last,
            path: pickedFile.path,
            bytes: await pickedFile.readAsBytes(),
            size: await pickedFile.length(),
          );

          final result = FilePickerResult([file]);

          return result;
        }
      } else {
        FilePickerResult? result = await FilePicker.platform.pickFiles(
          type: FileType.any,
          // type: imageUpload ? FileType.image : FileType.any,
          allowMultiple: uploadMultiple,
        );

        return result;
      }
    } catch (e) {
      Log.e('error $e');
      return null;
    }
    return null;
  }

  Future<void> _getPermission() async {
    if (await Permission.storage.isGranted) return;
    try {
      await Permission.storage.request();
    } catch (e) {
      Log.e('error $e');
    }
  }
}
