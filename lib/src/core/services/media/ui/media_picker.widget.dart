import 'dart:io';

import 'package:dawraq/src/core/extensions/context_extensions.dart';
import 'package:dawraq/src/core/services/media/controller/media_controller.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../theme/color_manager.dart';

class BaseMediaPickerWidget extends HookConsumerWidget {
  final bool withAddButton;
  final Alignment alignment;
  final List<(String id, String media)?> media;
  final bool showDeleteButton;

  const BaseMediaPickerWidget({
    super.key,
    this.withAddButton = true,
    this.alignment = Alignment.centerRight,
    this.media = const [],
    this.showDeleteButton = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaPickerController = ref.watch(mediaPickerControllerProvider);

    final filesPaths =
        mediaPickerController.filesPaths?.map((e) => e.path).toList() ?? [];

    final allMedia = [...media.map((e) => e?.$2), ...filesPaths];

    return WillPopScope(
      onWillPop: () async {
        mediaPickerController.clearFiles();
        return true;
      },
      child: allMedia.isEmpty
          ? _buildAddFileContainer(context, mediaPickerController)
          : ListView.separated(
              scrollDirection: Axis.horizontal,
              itemCount: allMedia.length,
              separatorBuilder: (context, index) => AppGaps.mediumGap,
              itemBuilder: (_, index) {
                final isNetwork =
                    media.map((e) => e?.$2).contains(allMedia[index]);

                return Row(
                  children: [
                    Stack(
                      children: [
                        ClipRRect(
                          borderRadius:
                              BorderRadius.circular(AppRadius.mediumRadius),
                          child: isNetwork
                              ? Image.network(allMedia[index]!,
                                  height: 100, width: 100, fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                  return const Center(
                                    child: Icon(
                                      Icons.picture_as_pdf,
                                      color: ColorManager.errorColor,
                                    ),
                                  );
                                })
                              : Image.file(File(allMedia[index]!),
                                  height: 100, width: 100, fit: BoxFit.cover,
                                  errorBuilder: (context, error, stackTrace) {
                                  return const SizedBox(
                                    height: 100,
                                    width: 100,
                                    child: Center(
                                      child: Icon(
                                        Icons.picture_as_pdf,
                                        color: ColorManager.primaryColor,
                                        size: 50,
                                      ),
                                    ),
                                  );
                                }),
                        ),
                        if (showDeleteButton)
                          IconButton(
                            icon: const CircleAvatar(
                              maxRadius: 15,
                              backgroundColor: Colors.white,
                              child: Icon(
                                Icons.close,
                                color: ColorManager.errorColor,
                                size: 18,
                              ),
                            ),
                            onPressed: () async {
                              if (isNetwork) {
                                media.removeAt(index);

                                return;
                              }

                              mediaPickerController.removeFile(index);
                            },
                          ),
                      ],
                    ),
                    if (index == allMedia.length - 1 && withAddButton) ...[
                      AppGaps.mediumGap,
                      _buildAddFileContainer(context, mediaPickerController,
                          networkPaths: media.map((e) => e?.$2 ?? '').toList()),
                    ],
                  ],
                );
              },
            ).sized(
              height: 120,
            ),
    );
  }

  Widget _buildAddFileContainer(
    BuildContext context,
    MediaPickerController mediaPickerController, {
    List<String> networkPaths = const [],
  }) {
    final filesPaths = mediaPickerController.filesPaths;
    if (!withAddButton && filesPaths.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Card(
          surfaceTintColor: ColorManager.white,
          elevation: 2,
          child: Container(
            height: 100,
            width: 100,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
            ),
            child: IconButton(
              icon: const Icon(
                Icons.add,
                color: ColorManager.black,
              ),
              onPressed: () async {
                // show dialog to ask camera or gallery
                //await mediaPickerController.pickFile(
                //                   allowMultiple: true,
                //                 );

                showModalBottomSheet(
                    context: context,
                    builder: (_) {
                      return Padding(
                        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            ListTile(
                              leading:
                                  const Icon(Icons.camera, color: Colors.black),
                              title: Text(context.tr.camera),
                              onTap: () async {
                                await mediaPickerController.pickFile(
                                  allowMultiple: true,
                                  useCamera: true,
                                );
                                Navigator.pop(context);
                              },
                            ),
                            ListTile(
                              leading:
                                  const Icon(Icons.image, color: Colors.black),
                              title: Text(context.tr.gallery),
                              onTap: () async {
                                await mediaPickerController.pickFile(
                                  allowMultiple: true,
                                );
                                Navigator.pop(context);
                              },
                            ),
                          ],
                        ),
                      );
                    });
              },
            ),
          ),
        ),
      ],
    );
  }
}
