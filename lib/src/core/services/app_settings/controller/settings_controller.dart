import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../repository/local_settings_repo.dart';

final settingsControllerProvider =
    ChangeNotifierProvider<AppSettingsController>((ref) {
  final settingsLocalRepo = ref.watch(settingsRepoProvider);
  return AppSettingsController(settingsLocalRepo: settingsLocalRepo)
    ..loadSettings();
});

class AppSettingsController extends BaseController {
  final SettingsLocalRepo settingsLocalRepo;

  AppSettingsController({required this.settingsLocalRepo});

  //! Load Settings ===================================
  Future<void> loadSettings() async {
    _locale = await settingsLocalRepo.locale();

    notifyListeners();
  }

  Locale _locale = const Locale('ar');
  // Locale _locale = const Locale('en', 'US');

  Locale get locale => _locale;

  bool get isEnglish => _locale.languageCode == 'en';

  //! Update Language  ===================================
  Future<void> updateLanguage(Locale newLocale) async {
    if (_locale == newLocale) return;
    _locale = newLocale;
    await settingsLocalRepo.updateLanguage(newLocale);
    notifyListeners();
  }
}
