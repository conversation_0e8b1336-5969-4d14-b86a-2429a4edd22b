import 'package:flutter/material.dart';

import '../../../generated/l10n.dart';

extension Localization on BuildContext {
  //? Localization shortcuts
  S get tr => S.of(this);
}

extension AppSettings on BuildContext {
  bool get isEnglish {
    final locale = Localizations.localeOf(this);
    return locale.languageCode == 'en';
  }

  String langText(String langCode) {
    if (langCode == 'en') return 'English';
    return 'العربية';
  }
}
