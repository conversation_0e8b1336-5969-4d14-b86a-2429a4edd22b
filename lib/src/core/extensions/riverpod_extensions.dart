import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

extension Riverpod<T> on AsyncValue<T> {
  //? Override .when() method to add a new case
  R get<R>({
    bool skipLoadingOnReload = false,
    bool skipLoadingOnRefresh = true,
    bool skipError = false,
    required R Function(T data) data,
    R Function(Object error, StackTrace stackTrace)? error,
    Function()? loading,
  }) {
    return when(
      skipError: skipError,
      skipLoadingOnRefresh: skipLoadingOnRefresh,
      skipLoadingOnReload: skipLoadingOnReload,
      data: data,
      error: (err, stack) {
        if (error != null) {
          return error(err, stack);
        } else {
          return const SizedBox() as R;
        }
      },
      loading: () {
        return loading != null ? loading() : const LoadingWidget();
      },
    );
  }
}
