import 'dart:convert';

import 'package:xr_helper/xr_helper.dart';

enum BaseMediaType {
  image,
  video,
  audio,
  file,
}

extension StringExtensions on String {
  String get translateErrorMessage {
    final RegExp messageRegex = RegExp(r'"message":"([^"]+)"');
    final match = messageRegex.firstMatch(this);
    final msg = (match != null ? match.group(1) : "Error occurred") ?? '';

    final decodedMsg = jsonDecode('"$msg"');

    Log.w('ERROR_MSG: $decodedMsg');

    return decodedMsg;
  }

  BaseMediaType getFileType() {
    final ext = split('.').last;
    switch (ext) {
      //? Images
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
      case 'ico':
      case 'raw':
      case 'indd':
      case 'ai':
      case 'eps':
        return BaseMediaType.image;
      //? Videos
      case 'mp4':
      case 'mkv':
      case 'flv':
      case 'avi':
      case 'mov':
      case 'wmv':
      case 'm4v':
      case 'webm':
      case 'mpg':
      case 'ogv':
        return BaseMediaType.video;
      //? Audios
      case 'mp3':
      case 'wav':
      case 'ogg':
      case 'flac':
      case 'aac':
      case 'wma':
      case 'm4a':
      case 'amr':
      case 'aiff':
        return BaseMediaType.audio;
      //? Documents
      case 'pdf':
      case 'doc':
      case 'docx':
      case 'xls':
      case 'xlsx':
      case 'ppt':
      case 'pptx':
      case 'txt':
      case 'csv':
      case 'rtf':
      case 'odt':
      case 'ods':
      case 'odp':
        return BaseMediaType.file;
      default:
        return BaseMediaType.image;
    }
  }

  // is video
  bool get isVideo => true;
  // getFileType() == BaseMediaType.video;

  // is image
  bool get isImage => getFileType() == BaseMediaType.image;
}
